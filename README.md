# MT5 JavaFX Trading App

Una aplicación JavaFX que se conecta con MetaTrader 5 para obtener datos históricos y precios en tiempo real.

## Características

- **Conexión con MetaTrader 5**: Integración completa con MT5 a través de Python
- **Precios en tiempo real**: Streaming de precios para múltiples símbolos
- **Datos históricos**: Visualización de datos OHLCV con diferentes timeframes
- **Interfaz moderna**: Interfaz JavaFX intuitiva y responsive
- **Múltiples símbolos**: Soporte para agregar y monitorear varios pares de divisas

## Requisitos

### Software necesario:
1. **Java 17 o superior**
2. **Maven 3.6+**
3. **Python 3.8+**
4. **MetaTrader 5** instalado y configurado

### Dependencias Python:
```bash
pip install -r requirements.txt
```

## Instalación

1. **Clonar o descargar el proyecto**

2. **Instalar dependencias Python:**
```bash
pip install -r requirements.txt
```

3. **Compilar el proyecto Java:**
```bash
mvn clean compile
```

4. **Ejecutar la aplicación:**
```bash
mvn javafx:run
```

## Uso

### 1. Conectar con MT5
- Asegúrate de que MetaTrader 5 esté abierto y conectado
- Presiona el botón "Conectar MT5" en la aplicación
- Verifica que el estado cambie a "Conectado"

### 2. Agregar símbolos para monitoreo
- Selecciona un símbolo del ComboBox (ej: EURUSD)
- Presiona "Agregar" para iniciar el streaming de precios
- Los precios aparecerán en la tabla de la izquierda

### 3. Ver datos históricos
- Selecciona un símbolo y timeframe
- Ajusta la cantidad de barras a cargar
- Presiona "Cargar Histórico"
- Los datos aparecerán en la tabla de la derecha

## Estructura del Proyecto

```
├── src/main/java/com/trading/app/
│   ├── TradingApp.java              # Clase principal
│   ├── controller/
│   │   └── MainController.java      # Controlador de la UI
│   ├── model/
│   │   ├── Price.java              # Modelo de precio
│   │   └── HistoricalData.java     # Modelo de datos históricos
│   └── service/
│       └── MT5Service.java         # Servicio de conexión MT5
├── src/main/resources/
│   └── fxml/
│       └── main.fxml               # Interfaz FXML
├── python/
│   └── mt5_bridge.py               # Script Python para MT5
├── requirements.txt                # Dependencias Python
└── pom.xml                        # Configuración Maven
```

## Configuración

### MetaTrader 5
1. Abre MetaTrader 5
2. Ve a Herramientas → Opciones → Expert Advisors
3. Marca "Permitir importaciones de DLL"
4. Marca "Permitir trading automático"

### Python
Asegúrate de que Python esté en el PATH del sistema y que las dependencias estén instaladas.

## Solución de Problemas

### Error de conexión con MT5
- Verifica que MT5 esté abierto y conectado a un servidor
- Asegúrate de que Python y las dependencias estén correctamente instaladas
- Revisa que el script Python tenga permisos de ejecución

### Problemas con JavaFX
- Verifica que estés usando Java 17 o superior
- Asegúrate de que las dependencias de JavaFX estén correctamente configuradas

### Datos no aparecen
- Verifica que el símbolo esté disponible en tu broker
- Asegúrate de que tengas permisos para acceder a los datos del símbolo

## Desarrollo

### Compilar y ejecutar en desarrollo:
```bash
mvn clean javafx:run
```

### Crear JAR ejecutable:
```bash
mvn clean package
```

### Ejecutar tests:
```bash
mvn test
```

## Contribuir

1. Fork el proyecto
2. Crea una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abre un Pull Request

## Licencia

Este proyecto está bajo la Licencia MIT. Ver el archivo `LICENSE` para más detalles.

## Contacto

Para preguntas o soporte, por favor abre un issue en el repositorio.
