#!/usr/bin/env python3
"""
Script de prueba para verificar la conexión con MetaTrader 5
"""

import sys
import os

# Agregar el directorio python al path
sys.path.append(os.path.join(os.path.dirname(__file__), 'python'))

try:
    import MetaTrader5 as mt5
    print("✓ MetaTrader5 library importada correctamente")
    
    # Intentar inicializar MT5
    if mt5.initialize():
        print("✓ MetaTrader 5 inicializado correctamente")
        
        # Obtener información de la cuenta
        account_info = mt5.account_info()
        if account_info:
            print(f"✓ Cuenta: {account_info.login}")
            print(f"✓ Servidor: {account_info.server}")
            print(f"✓ Balance: {account_info.balance}")
        else:
            print("⚠ No se pudo obtener información de la cuenta")
        
        # Probar obtener un tick
        tick = mt5.symbol_info_tick("EURUSD")
        if tick:
            print(f"✓ Precio EURUSD - Bid: {tick.bid}, Ask: {tick.ask}")
        else:
            print("⚠ No se pudo obtener precio de EURUSD")
        
        mt5.shutdown()
        print("✓ MetaTrader 5 cerrado correctamente")
        
    else:
        print("✗ Error: No se pudo inicializar MetaTrader 5")
        print("  Asegúrate de que MetaTrader 5 esté abierto y conectado")
        
except ImportError as e:
    print(f"✗ Error: No se pudo importar MetaTrader5: {e}")
    print("  Ejecuta: pip install MetaTrader5")
except Exception as e:
    print(f"✗ Error inesperado: {e}")

print("\n" + "="*50)
print("INSTRUCCIONES:")
print("1. Abre MetaTrader 5")
print("2. Conecta a tu broker")
print("3. Ejecuta la aplicación JavaFX con: mvn javafx:run")
print("4. Presiona 'Conectar MT5' en la aplicación")
print("="*50)
