package com.trading.app.indicator;

import com.trading.app.model.Candlestick;
import javafx.scene.canvas.GraphicsContext;
import javafx.scene.paint.Color;

import java.util.List;

/**
 * Indicador de Media Móvil Exponencial (EMA)
 */
public class ExponentialMovingAverageIndicator extends TechnicalIndicator {
    private double multiplier;

    public ExponentialMovingAverageIndicator(int period) {
        super("EMA", Color.ORANGE, period);
        this.multiplier = 2.0 / (period + 1);
    }

    public ExponentialMovingAverageIndicator(int period, Color color) {
        super("EMA", color, period);
        this.multiplier = 2.0 / (period + 1);
    }

    @Override
    public double[] calculate(List<Candlestick> candlesticks) {
        if (candlesticks == null || candlesticks.size() < period) {
            return new double[0];
        }

        double[] emaValues = new double[candlesticks.size()];
        
        // Calcular SMA inicial para el primer valor EMA
        double sum = 0;
        for (int i = 0; i < period; i++) {
            emaValues[i] = Double.NaN;
            sum += candlesticks.get(i).getClose();
        }
        
        // Primer valor EMA es la SMA
        emaValues[period - 1] = sum / period;
        
        // Calcular EMA para el resto de valores
        for (int i = period; i < candlesticks.size(); i++) {
            double currentPrice = candlesticks.get(i).getClose();
            emaValues[i] = (currentPrice * multiplier) + (emaValues[i - 1] * (1 - multiplier));
        }
        
        return emaValues;
    }

    @Override
    public void draw(GraphicsContext gc, List<Candlestick> candlesticks, double[] values,
                     double chartWidth, double chartHeight, double marginLeft, double marginTop,
                     double minPrice, double maxPrice, double candleWidth, double zoomFactor, double panX) {
        
        if (!visible || values == null || values.length == 0) return;
        
        drawLine(gc, candlesticks, values, chartWidth, chartHeight, marginLeft, marginTop,
                minPrice, maxPrice, candleWidth, zoomFactor, panX);
    }

    @Override
    public boolean isOverlay() {
        return true; // Se dibuja sobre el gráfico principal
    }

    @Override
    public void setPeriod(int period) {
        super.setPeriod(period);
        this.multiplier = 2.0 / (period + 1);
    }
}
