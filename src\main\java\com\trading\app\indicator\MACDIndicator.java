package com.trading.app.indicator;

import com.trading.app.model.Candlestick;
import javafx.scene.canvas.GraphicsContext;
import javafx.scene.paint.Color;

import java.util.List;

/**
 * Indicador MACD (Moving Average Convergence Divergence)
 */
public class MACDIndicator extends TechnicalIndicator {
    private int fastPeriod;
    private int slowPeriod;
    private int signalPeriod;
    
    private double[] macdLine;
    private double[] signalLine;
    private double[] histogram;

    public MACDIndicator() {
        this(12, 26, 9);
    }

    public MACDIndicator(int fastPeriod, int slowPeriod, int signalPeriod) {
        super("MACD", Color.BLUE, slowPeriod);
        this.fastPeriod = fastPeriod;
        this.slowPeriod = slowPeriod;
        this.signalPeriod = signalPeriod;
    }

    @Override
    public double[] calculate(List<Candlestick> candlesticks) {
        if (candlesticks == null || candlesticks.size() < slowPeriod) {
            return new double[0];
        }

        // Calcular EMAs
        ExponentialMovingAverageIndicator fastEMA = new ExponentialMovingAverageIndicator(fastPeriod);
        ExponentialMovingAverageIndicator slowEMA = new ExponentialMovingAverageIndicator(slowPeriod);
        
        double[] fastEMAValues = fastEMA.calculate(candlesticks);
        double[] slowEMAValues = slowEMA.calculate(candlesticks);
        
        // Calcular línea MACD
        macdLine = new double[candlesticks.size()];
        for (int i = 0; i < candlesticks.size(); i++) {
            if (Double.isNaN(fastEMAValues[i]) || Double.isNaN(slowEMAValues[i])) {
                macdLine[i] = Double.NaN;
            } else {
                macdLine[i] = fastEMAValues[i] - slowEMAValues[i];
            }
        }
        
        // Calcular línea de señal (EMA de la línea MACD)
        signalLine = calculateSignalLine(macdLine);
        
        // Calcular histograma
        histogram = new double[candlesticks.size()];
        for (int i = 0; i < candlesticks.size(); i++) {
            if (Double.isNaN(macdLine[i]) || Double.isNaN(signalLine[i])) {
                histogram[i] = Double.NaN;
            } else {
                histogram[i] = macdLine[i] - signalLine[i];
            }
        }
        
        return macdLine; // Retorna la línea MACD principal
    }

    private double[] calculateSignalLine(double[] macdValues) {
        double[] signal = new double[macdValues.length];
        double multiplier = 2.0 / (signalPeriod + 1);
        
        // Encontrar el primer valor válido para inicializar
        int startIndex = -1;
        double sum = 0;
        int count = 0;
        
        for (int i = 0; i < macdValues.length && count < signalPeriod; i++) {
            if (!Double.isNaN(macdValues[i])) {
                if (startIndex == -1) startIndex = i;
                sum += macdValues[i];
                count++;
            }
            signal[i] = Double.NaN;
        }
        
        if (count == signalPeriod) {
            signal[startIndex + signalPeriod - 1] = sum / signalPeriod;
            
            // Calcular EMA para el resto
            for (int i = startIndex + signalPeriod; i < macdValues.length; i++) {
                if (!Double.isNaN(macdValues[i])) {
                    signal[i] = (macdValues[i] * multiplier) + (signal[i - 1] * (1 - multiplier));
                } else {
                    signal[i] = signal[i - 1];
                }
            }
        }
        
        return signal;
    }

    @Override
    public void draw(GraphicsContext gc, List<Candlestick> candlesticks, double[] values,
                     double chartWidth, double chartHeight, double marginLeft, double marginTop,
                     double minPrice, double maxPrice, double candleWidth, double zoomFactor, double panX) {
        
        if (!visible || values == null || values.length == 0) return;
        
        // Obtener rango de valores para MACD
        double[] range = getValueRange(values);
        double minValue = range[0];
        double maxValue = range[1];
        
        // Dibujar línea cero
        gc.setStroke(Color.GRAY);
        gc.setLineWidth(0.5);
        double zeroY = marginTop + ((maxValue - 0) / (maxValue - minValue)) * chartHeight;
        gc.strokeLine(marginLeft, zeroY, marginLeft + chartWidth, zeroY);
        
        // Dibujar histograma
        if (histogram != null) {
            drawHistogram(gc, candlesticks, histogram, chartWidth, chartHeight, marginLeft, marginTop,
                         minValue, maxValue, candleWidth, zoomFactor, panX, zeroY);
        }
        
        // Dibujar línea MACD
        gc.setStroke(Color.BLUE);
        gc.setLineWidth(1.5);
        drawLine(gc, candlesticks, macdLine, chartWidth, chartHeight, marginLeft, marginTop,
                minValue, maxValue, candleWidth, zoomFactor, panX);
        
        // Dibujar línea de señal
        if (signalLine != null) {
            gc.setStroke(Color.RED);
            gc.setLineWidth(1.0);
            drawLine(gc, candlesticks, signalLine, chartWidth, chartHeight, marginLeft, marginTop,
                    minValue, maxValue, candleWidth, zoomFactor, panX);
        }
    }

    private void drawHistogram(GraphicsContext gc, List<Candlestick> candlesticks, double[] histValues,
                              double chartWidth, double chartHeight, double marginLeft, double marginTop,
                              double minValue, double maxValue, double candleWidth, double zoomFactor, 
                              double panX, double zeroY) {
        
        double totalCandleWidth = (candleWidth + 2) * zoomFactor;
        double startX = marginLeft + panX;
        double valueRange = maxValue - minValue;
        
        for (int i = 0; i < Math.min(histValues.length, candlesticks.size()); i++) {
            if (Double.isNaN(histValues[i])) continue;
            
            double x = startX + (i * totalCandleWidth);
            
            // Solo dibujar barras visibles
            if (x < 0 || x > chartWidth + marginLeft) continue;
            
            double barHeight = Math.abs(histValues[i] / valueRange * chartHeight);
            double y = histValues[i] >= 0 ? zeroY - barHeight : zeroY;
            
            // Color del histograma
            if (histValues[i] >= 0) {
                gc.setFill(Color.GREEN.deriveColor(0, 1, 1, 0.7));
            } else {
                gc.setFill(Color.RED.deriveColor(0, 1, 1, 0.7));
            }
            
            gc.fillRect(x, y, candleWidth * zoomFactor * 0.8, barHeight);
        }
    }

    @Override
    public boolean isOverlay() {
        return false; // Se dibuja en subpanel
    }

    @Override
    public double[] getValueRange(double[] values) {
        // Considerar todos los valores: MACD, señal e histograma
        double min = Double.MAX_VALUE;
        double max = Double.MIN_VALUE;
        
        double[][] allValues = {macdLine, signalLine, histogram};
        
        for (double[] valueArray : allValues) {
            if (valueArray != null) {
                for (double value : valueArray) {
                    if (!Double.isNaN(value)) {
                        min = Math.min(min, value);
                        max = Math.max(max, value);
                    }
                }
            }
        }
        
        if (min == Double.MAX_VALUE) {
            return new double[]{-1, 1};
        }
        
        // Agregar margen
        double range = max - min;
        double margin = range * 0.1;
        
        return new double[]{min - margin, max + margin};
    }

    // Getters para acceder a las líneas individuales
    public double[] getMACDLine() {
        return macdLine;
    }

    public double[] getSignalLine() {
        return signalLine;
    }

    public double[] getHistogram() {
        return histogram;
    }
}
