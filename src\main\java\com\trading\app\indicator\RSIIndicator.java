package com.trading.app.indicator;

import com.trading.app.model.Candlestick;
import javafx.scene.canvas.GraphicsContext;
import javafx.scene.paint.Color;

import java.util.List;

/**
 * Indicador RSI (Relative Strength Index)
 */
public class RSIIndicator extends TechnicalIndicator {

    public RSIIndicator(int period) {
        super("RSI", Color.PURPLE, period);
    }

    public RSIIndicator(int period, Color color) {
        super("RSI", color, period);
    }

    @Override
    public double[] calculate(List<Candlestick> candlesticks) {
        if (candlesticks == null || candlesticks.size() < period + 1) {
            return new double[0];
        }

        double[] rsiValues = new double[candlesticks.size()];
        double[] gains = new double[candlesticks.size()];
        double[] losses = new double[candlesticks.size()];
        
        // Calcular ganancias y pérdidas
        for (int i = 1; i < candlesticks.size(); i++) {
            double change = candlesticks.get(i).getClose() - candlesticks.get(i - 1).getClose();
            gains[i] = Math.max(change, 0);
            losses[i] = Math.max(-change, 0);
        }
        
        // Calcular promedios iniciales
        double avgGain = 0;
        double avgLoss = 0;
        
        for (int i = 1; i <= period; i++) {
            avgGain += gains[i];
            avgLoss += losses[i];
        }
        
        avgGain /= period;
        avgLoss /= period;
        
        // Calcular RSI
        for (int i = 0; i < candlesticks.size(); i++) {
            if (i < period) {
                rsiValues[i] = Double.NaN;
            } else if (i == period) {
                if (avgLoss == 0) {
                    rsiValues[i] = 100;
                } else {
                    double rs = avgGain / avgLoss;
                    rsiValues[i] = 100 - (100 / (1 + rs));
                }
            } else {
                // Suavizado exponencial
                avgGain = ((avgGain * (period - 1)) + gains[i]) / period;
                avgLoss = ((avgLoss * (period - 1)) + losses[i]) / period;
                
                if (avgLoss == 0) {
                    rsiValues[i] = 100;
                } else {
                    double rs = avgGain / avgLoss;
                    rsiValues[i] = 100 - (100 / (1 + rs));
                }
            }
        }
        
        return rsiValues;
    }

    @Override
    public void draw(GraphicsContext gc, List<Candlestick> candlesticks, double[] values,
                     double chartWidth, double chartHeight, double marginLeft, double marginTop,
                     double minPrice, double maxPrice, double candleWidth, double zoomFactor, double panX) {
        
        if (!visible || values == null || values.length == 0) return;
        
        // RSI se dibuja en su propio rango (0-100)
        drawLine(gc, candlesticks, values, chartWidth, chartHeight, marginLeft, marginTop,
                0, 100, candleWidth, zoomFactor, panX);
        
        // Dibujar líneas de referencia (30 y 70)
        gc.setStroke(Color.LIGHTGRAY);
        gc.setLineWidth(0.5);
        
        double y30 = marginTop + ((100 - 30) / 100.0) * chartHeight;
        double y70 = marginTop + ((100 - 70) / 100.0) * chartHeight;
        
        gc.strokeLine(marginLeft, y30, marginLeft + chartWidth, y30);
        gc.strokeLine(marginLeft, y70, marginLeft + chartWidth, y70);
        
        // Etiquetas
        gc.setFill(Color.GRAY);
        gc.fillText("30", marginLeft - 20, y30 + 3);
        gc.fillText("70", marginLeft - 20, y70 + 3);
    }

    @Override
    public boolean isOverlay() {
        return false; // Se dibuja en subpanel
    }

    @Override
    public double[] getValueRange(double[] values) {
        return new double[]{0, 100}; // RSI siempre está entre 0 y 100
    }
}
