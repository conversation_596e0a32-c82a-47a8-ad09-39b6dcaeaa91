#!/usr/bin/env python3
"""
Script Python para conectar con MetaTrader 5 y proporcionar datos a la aplicación JavaFX
"""

import sys
import json
import time
import MetaTrader5 as mt5
from datetime import datetime, timedelta
import pandas as pd

def initialize_mt5():
    """Inicializa la conexión con MT5"""
    try:
        # Inicializar MT5
        if not mt5.initialize():
            return {"status": "error", "message": "No se pudo inicializar MT5"}
        
        # Verificar conexión
        account_info = mt5.account_info()
        if account_info is None:
            return {"status": "error", "message": "No se pudo obtener información de la cuenta"}
        
        return {
            "status": "connected",
            "account": account_info.login,
            "server": account_info.server,
            "balance": account_info.balance
        }
    except Exception as e:
        return {"status": "error", "message": str(e)}

def get_symbol_price(symbol):
    """Obtiene el precio actual de un símbolo"""
    try:
        # Asegurar que MT5 esté inicializado
        if not mt5.initialize():
            return {"error": "No se pudo inicializar MT5"}

        # Verificar que el símbolo esté disponible
        symbol_info = mt5.symbol_info(symbol)
        if symbol_info is None:
            return {"error": f"Símbolo {symbol} no encontrado"}

        # Asegurar que el símbolo esté visible en Market Watch
        if not symbol_info.visible:
            if not mt5.symbol_select(symbol, True):
                return {"error": f"No se pudo seleccionar el símbolo {symbol}"}

        # Obtener tick actual
        tick = mt5.symbol_info_tick(symbol)
        if tick is None:
            return {"error": f"No se pudo obtener precio para {symbol}. Error MT5: {mt5.last_error()}"}

        return {
            "symbol": symbol,
            "bid": tick.bid,
            "ask": tick.ask,
            "last": tick.last,
            "volume": tick.volume,
            "time": datetime.fromtimestamp(tick.time).isoformat()
        }
    except Exception as e:
        return {"error": str(e)}

def get_historical_data(symbol, timeframe, count):
    """Obtiene datos históricos para un símbolo"""
    try:
        # Asegurar que MT5 esté inicializado
        if not mt5.initialize():
            return {"error": "No se pudo inicializar MT5"}

        # Verificar que el símbolo esté disponible
        symbol_info = mt5.symbol_info(symbol)
        if symbol_info is None:
            return {"error": f"Símbolo {symbol} no encontrado"}

        # Asegurar que el símbolo esté visible en Market Watch
        if not symbol_info.visible:
            if not mt5.symbol_select(symbol, True):
                return {"error": f"No se pudo seleccionar el símbolo {symbol}"}

        # Mapear timeframes
        timeframe_map = {
            "M1": mt5.TIMEFRAME_M1,
            "M5": mt5.TIMEFRAME_M5,
            "M15": mt5.TIMEFRAME_M15,
            "M30": mt5.TIMEFRAME_M30,
            "H1": mt5.TIMEFRAME_H1,
            "H4": mt5.TIMEFRAME_H4,
            "D1": mt5.TIMEFRAME_D1,
            "W1": mt5.TIMEFRAME_W1,
            "MN1": mt5.TIMEFRAME_MN1
        }

        mt5_timeframe = timeframe_map.get(timeframe, mt5.TIMEFRAME_M15)

        # Obtener datos históricos
        rates = mt5.copy_rates_from_pos(symbol, mt5_timeframe, 0, count)
        if rates is None or len(rates) == 0:
            # Intentar con un método alternativo
            rates = mt5.copy_rates_from(symbol, mt5_timeframe, datetime.now(), count)
            if rates is None or len(rates) == 0:
                return {"error": f"No se pudieron obtener datos históricos para {symbol}. Error MT5: {mt5.last_error()}"}

        # Convertir a formato JSON
        data = []
        for rate in rates:
            data.append({
                "time": datetime.fromtimestamp(rate['time']).isoformat(),
                "open": float(rate['open']),
                "high": float(rate['high']),
                "low": float(rate['low']),
                "close": float(rate['close']),
                "volume": int(rate['tick_volume'])
            })

        return {"data": data}
    except Exception as e:
        return {"error": str(e)}

def stream_prices(symbol):
    """Stream de precios en tiempo real para un símbolo"""
    try:
        # Asegurar que MT5 esté inicializado
        if not mt5.initialize():
            print(json.dumps({"error": "No se pudo inicializar MT5"}))
            return

        # Verificar que el símbolo esté disponible
        symbol_info = mt5.symbol_info(symbol)
        if symbol_info is None:
            print(json.dumps({"error": f"Símbolo {symbol} no encontrado"}))
            return

        # Asegurar que el símbolo esté visible en Market Watch
        if not symbol_info.visible:
            if not mt5.symbol_select(symbol, True):
                print(json.dumps({"error": f"No se pudo seleccionar el símbolo {symbol}"}))
                return

        last_tick_time = 0

        while True:
            tick = mt5.symbol_info_tick(symbol)
            if tick is None:
                time.sleep(0.1)
                continue

            # Solo enviar si hay un nuevo tick
            if tick.time > last_tick_time:
                price_data = {
                    "symbol": symbol,
                    "bid": tick.bid,
                    "ask": tick.ask,
                    "last": tick.last,
                    "volume": tick.volume,
                    "time": datetime.fromtimestamp(tick.time).isoformat()
                }
                print(json.dumps(price_data))
                sys.stdout.flush()
                last_tick_time = tick.time

            time.sleep(0.1)  # Pausa pequeña para no sobrecargar

    except KeyboardInterrupt:
        pass
    except Exception as e:
        print(json.dumps({"error": str(e)}))

def main():
    if len(sys.argv) < 2:
        print(json.dumps({"error": "Comando no especificado"}))
        return
    
    command = sys.argv[1]
    
    if command == "init":
        result = initialize_mt5()
        print(json.dumps(result))
    
    elif command == "price":
        if len(sys.argv) < 3:
            print(json.dumps({"error": "Símbolo no especificado"}))
            return
        symbol = sys.argv[2]
        result = get_symbol_price(symbol)
        print(json.dumps(result))
    
    elif command == "history":
        if len(sys.argv) < 5:
            print(json.dumps({"error": "Parámetros insuficientes para datos históricos"}))
            return
        symbol = sys.argv[2]
        timeframe = sys.argv[3]
        count = int(sys.argv[4])
        result = get_historical_data(symbol, timeframe, count)
        print(json.dumps(result))
    
    elif command == "stream":
        if len(sys.argv) < 3:
            print(json.dumps({"error": "Símbolo no especificado para streaming"}))
            return
        symbol = sys.argv[2]
        stream_prices(symbol)
    
    else:
        print(json.dumps({"error": f"Comando desconocido: {command}"}))

if __name__ == "__main__":
    main()
