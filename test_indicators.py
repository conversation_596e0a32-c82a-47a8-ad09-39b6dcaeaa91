#!/usr/bin/env python3
"""
Script para probar los indicadores técnicos implementados
"""

import subprocess
import json
import time
import sys

def test_indicators():
    """Prueba los indicadores técnicos"""
    print("="*70)
    print("PRUEBA DE INDICADORES TÉCNICOS")
    print("="*70)
    
    # Test 1: Verificar compilación
    print("\n1. Verificando compilación...")
    try:
        result = subprocess.run(["mvn", "compile"], 
                              capture_output=True, text=True, timeout=60)
        if result.returncode == 0:
            print("✅ Compilación exitosa")
        else:
            print(f"❌ Error en compilación: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Error compilando: {e}")
        return False
    
    # Test 2: Verificar archivos de indicadores
    print("\n2. Verificando archivos de indicadores...")
    import os
    
    indicator_files = [
        "src/main/java/com/trading/app/indicator/TechnicalIndicator.java",
        "src/main/java/com/trading/app/indicator/MovingAverageIndicator.java",
        "src/main/java/com/trading/app/indicator/ExponentialMovingAverageIndicator.java",
        "src/main/java/com/trading/app/indicator/RSIIndicator.java",
        "src/main/java/com/trading/app/indicator/MACDIndicator.java",
        "src/main/java/com/trading/app/indicator/BollingerBandsIndicator.java",
        "src/main/java/com/trading/app/indicator/StochasticIndicator.java"
    ]
    
    for file_path in indicator_files:
        if os.path.exists(file_path):
            print(f"✅ {os.path.basename(file_path)}")
        else:
            print(f"❌ {file_path} - NO ENCONTRADO")
    
    # Test 3: Verificar integración con gráfico
    print("\n3. Verificando integración con gráfico...")
    chart_files = [
        "src/main/java/com/trading/app/component/CandlestickChart.java",
        "src/main/java/com/trading/app/controller/ChartController.java",
        "src/main/resources/fxml/chart.fxml"
    ]
    
    for file_path in chart_files:
        if os.path.exists(file_path):
            print(f"✅ {os.path.basename(file_path)}")
        else:
            print(f"❌ {file_path} - NO ENCONTRADO")
    
    # Test 4: Verificar datos de MT5 para indicadores
    print("\n4. Verificando datos de MT5 para cálculos...")
    try:
        result = subprocess.run([
            "python", "python/mt5_bridge.py", "history", "EURUSD", "M15", "100"
        ], capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0:
            data = json.loads(result.stdout.strip())
            if "data" in data and len(data["data"]) >= 50:
                print(f"✅ Datos suficientes para indicadores: {len(data['data'])} velas")
            else:
                print(f"⚠️  Pocos datos para indicadores: {len(data.get('data', []))} velas")
        else:
            print(f"❌ Error obteniendo datos: {result.stderr}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("\n" + "="*70)
    print("INDICADORES TÉCNICOS IMPLEMENTADOS")
    print("="*70)
    
    indicators_info = [
        ("📈 Media Móvil Simple (SMA)", "Promedio de precios en N períodos", "Overlay"),
        ("📊 Media Móvil Exponencial (EMA)", "Promedio ponderado exponencialmente", "Overlay"),
        ("📉 Bandas de Bollinger", "SMA ± desviaciones estándar", "Overlay"),
        ("⚡ RSI", "Índice de Fuerza Relativa (0-100)", "Subpanel"),
        ("🔄 MACD", "Convergencia/Divergencia de medias", "Subpanel"),
        ("🎯 Estocástico", "Oscilador %K y %D (0-100)", "Subpanel")
    ]
    
    for name, description, panel_type in indicators_info:
        print(f"{name}")
        print(f"   Descripción: {description}")
        print(f"   Tipo: {panel_type}")
        print()
    
    print("="*70)
    print("FUNCIONALIDADES DE LOS INDICADORES")
    print("="*70)
    print("✅ Cálculo automático basado en datos históricos")
    print("✅ Actualización en tiempo real")
    print("✅ Activación/desactivación con botones")
    print("✅ Indicadores overlay (sobre el gráfico principal)")
    print("✅ Indicadores de subpanel (ventana separada)")
    print("✅ Colores personalizados para cada indicador")
    print("✅ Zoom y pan compatibles")
    print("✅ Parámetros configurables")
    
    print("\n" + "="*70)
    print("INSTRUCCIONES DE USO")
    print("="*70)
    print("1. Ejecuta: mvn javafx:run")
    print("2. Conecta a MT5")
    print("3. Ve a la pestaña '📈 Gráfico'")
    print("4. Carga datos históricos")
    print("5. Haz clic en los botones de indicadores:")
    print("   • SMA - Media móvil simple (azul)")
    print("   • EMA - Media móvil exponencial (naranja)")
    print("   • Bollinger - Bandas de Bollinger (púrpura)")
    print("   • RSI - Índice de fuerza relativa (púrpura)")
    print("   • MACD - Convergencia/divergencia (azul)")
    print("   • Stochastic - Oscilador estocástico (rojo)")
    print("6. Los botones se iluminarán cuando estén activos")
    print("7. Haz clic nuevamente para desactivar")
    
    print("\n" + "="*70)
    print("CARACTERÍSTICAS TÉCNICAS")
    print("="*70)
    print("• SMA(20) - Media móvil de 20 períodos")
    print("• EMA(20) - Media exponencial de 20 períodos")
    print("• Bollinger(20,2) - 20 períodos, 2 desviaciones")
    print("• RSI(14) - 14 períodos, niveles 30/70")
    print("• MACD(12,26,9) - EMAs 12/26, señal 9")
    print("• Stochastic(14,3) - %K 14, %D 3, niveles 20/80")
    print("="*70)
    
    return True

if __name__ == "__main__":
    try:
        success = test_indicators()
        if success:
            print("\n🎉 ¡Indicadores técnicos listos para usar!")
            print("💡 Tip: Los indicadores se calculan automáticamente")
            print("💡 Tip: Usa zoom para ver detalles de los indicadores")
        else:
            print("\n❌ Hay problemas que resolver")
    except KeyboardInterrupt:
        print("\n\nPrueba interrumpida por el usuario")
    except Exception as e:
        print(f"\n❌ Error inesperado: {e}")
