import java.io.*;

public class debug_java_connection {
    public static void main(String[] args) {
        try {
            ProcessBuilder pb = new ProcessBuilder("python", "python/mt5_bridge.py", "init");
            pb.redirectErrorStream(true);
            Process process = pb.start();
            
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line = reader.readLine();
            
            System.out.println("Respuesta recibida: '" + line + "'");
            System.out.println("Longitud: " + (line != null ? line.length() : "null"));
            
            if (line != null) {
                System.out.println("Contiene 'status': " + line.contains("\"status\""));
                System.out.println("Contiene 'connected': " + line.contains("\"connected\""));
                System.out.println("Contiene 'status\":\"connected': " + line.contains("\"status\":\"connected\""));
                
                // Mostrar cada carácter
                System.out.println("Caracteres:");
                for (int i = 0; i < Math.min(line.length(), 100); i++) {
                    char c = line.charAt(i);
                    System.out.print(c + "(" + (int)c + ") ");
                }
                System.out.println();
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
