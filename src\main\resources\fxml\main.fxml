<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.trading.app.controller.MainController">
   <top>
      <VBox>
         <children>
            <!-- <PERSON><PERSON> de herramient<PERSON> -->
            <ToolBar>
               <items>
                  <Button fx:id="connectButton" mnemonicParsing="false" onAction="#connectToMT5" text="Conectar MT5" />
                  <Separator orientation="VERTICAL" />
                  <Label text="Símbolo:" />
                  <ComboBox fx:id="symbolComboBox" editable="true" prefWidth="120.0" promptText="EURUSD" />
                  <Button fx:id="addSymbolButton" mnemonicParsing="false" onAction="#addSymbol" text="Agregar" />
                  <Separator orientation="VERTICAL" />
                  <Label text="Timeframe:" />
                  <ComboBox fx:id="timeframeComboBox" prefWidth="80.0" />
                  <Button fx:id="loadHistoryButton" mnemonicParsing="false" onAction="#loadHistoricalData" text="Cargar Histórico" />
               </items>
            </ToolBar>
            
            <!-- Barra de estado -->
            <HBox alignment="CENTER_LEFT" spacing="10.0" style="-fx-background-color: #f0f0f0;">
               <children>
                  <Label fx:id="statusLabel" text="Desconectado" />
                  <Separator orientation="VERTICAL" />
                  <Label fx:id="connectionTimeLabel" text="--:--:--" />
               </children>
               <padding>
                  <Insets bottom="5.0" left="10.0" right="10.0" top="5.0" />
               </padding>
            </HBox>
         </children>
      </VBox>
   </top>
   
   <center>
      <SplitPane dividerPositions="0.6" orientation="HORIZONTAL">
         <items>
            <!-- Panel izquierdo: Precios en tiempo real -->
            <VBox spacing="10.0">
               <children>
                  <Label text="Precios en Tiempo Real">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>
                  
                  <TableView fx:id="pricesTable" prefHeight="300.0">
                     <columns>
                        <TableColumn fx:id="symbolColumn" prefWidth="80.0" text="Símbolo" />
                        <TableColumn fx:id="bidColumn" prefWidth="80.0" text="Bid" />
                        <TableColumn fx:id="askColumn" prefWidth="80.0" text="Ask" />
                        <TableColumn fx:id="lastColumn" prefWidth="80.0" text="Último" />
                        <TableColumn fx:id="spreadColumn" prefWidth="60.0" text="Spread" />
                        <TableColumn fx:id="volumeColumn" prefWidth="80.0" text="Volumen" />
                        <TableColumn fx:id="timeColumn" prefWidth="120.0" text="Hora" />
                     </columns>
                  </TableView>
                  
                  <!-- Panel de detalles del precio seleccionado -->
                  <VBox spacing="5.0" style="-fx-border-color: #cccccc; -fx-border-width: 1; -fx-padding: 10;">
                     <children>
                        <Label text="Detalles del Precio Seleccionado">
                           <font>
                              <Font name="System Bold" size="12.0" />
                           </font>
                        </Label>
                        <GridPane hgap="10.0" vgap="5.0">
                           <columnConstraints>
                              <ColumnConstraints hgrow="NEVER" minWidth="60.0" />
                              <ColumnConstraints hgrow="ALWAYS" />
                           </columnConstraints>
                           <children>
                              <Label text="Símbolo:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                              <Label fx:id="detailSymbolLabel" text="--" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                              <Label text="Bid:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                              <Label fx:id="detailBidLabel" text="--" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                              <Label text="Ask:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                              <Label fx:id="detailAskLabel" text="--" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                              <Label text="Spread:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                              <Label fx:id="detailSpreadLabel" text="--" GridPane.columnIndex="1" GridPane.rowIndex="3" />
                           </children>
                        </GridPane>
                     </children>
                  </VBox>
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="5.0" top="10.0" />
               </padding>
            </VBox>
            
            <!-- Panel derecho: Datos históricos -->
            <VBox spacing="10.0">
               <children>
                  <Label text="Datos Históricos">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>
                  
                  <TableView fx:id="historicalTable" prefHeight="400.0">
                     <columns>
                        <TableColumn fx:id="histTimeColumn" prefWidth="120.0" text="Fecha/Hora" />
                        <TableColumn fx:id="histOpenColumn" prefWidth="70.0" text="Open" />
                        <TableColumn fx:id="histHighColumn" prefWidth="70.0" text="High" />
                        <TableColumn fx:id="histLowColumn" prefWidth="70.0" text="Low" />
                        <TableColumn fx:id="histCloseColumn" prefWidth="70.0" text="Close" />
                        <TableColumn fx:id="histVolumeColumn" prefWidth="80.0" text="Volumen" />
                     </columns>
                  </TableView>
                  
                  <!-- Controles para datos históricos -->
                  <HBox alignment="CENTER_LEFT" spacing="10.0">
                     <children>
                        <Label text="Cantidad:" />
                        <Spinner fx:id="countSpinner" initialValue="100" max="1000" min="10" prefWidth="80.0" />
                        <Button fx:id="refreshHistoryButton" mnemonicParsing="false" onAction="#refreshHistoricalData" text="Actualizar" />
                     </children>
                  </HBox>
               </children>
               <padding>
                  <Insets bottom="10.0" left="5.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
         </items>
      </SplitPane>
   </center>
   
   <bottom>
      <HBox alignment="CENTER_LEFT" spacing="10.0" style="-fx-background-color: #f0f0f0;">
         <children>
            <Label text="Log:" />
            <TextArea fx:id="logTextArea" editable="false" prefHeight="80.0" wrapText="true" HBox.hgrow="ALWAYS" />
         </children>
         <padding>
            <Insets bottom="5.0" left="10.0" right="10.0" top="5.0" />
         </padding>
      </HBox>
   </bottom>
</BorderPane>
