package com.trading.app;

import javafx.application.Application;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.image.Image;
import javafx.stage.Stage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Clase principal de la aplicación JavaFX para trading con MetaTrader 5
 */
public class TradingApp extends Application {
    private static final Logger logger = LoggerFactory.getLogger(TradingApp.class);
    
    @Override
    public void start(Stage primaryStage) {
        try {
            // Cargar el archivo FXML
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/main.fxml"));
            Parent root = loader.load();
            
            // Configurar la escena
            Scene scene = new Scene(root, 1200, 800);
            
            // Configurar la ventana principal
            primaryStage.setTitle("MT5 Trading App - JavaFX");
            primaryStage.setScene(scene);
            primaryStage.setMinWidth(800);
            primaryStage.setMinHeight(600);
            
            // Agregar icono si existe
            try {
                Image icon = new Image(getClass().getResourceAsStream("/images/icon.png"));
                primaryStage.getIcons().add(icon);
            } catch (Exception e) {
                logger.warn("No se pudo cargar el icono de la aplicación");
            }
            
            // Configurar el comportamiento al cerrar
            primaryStage.setOnCloseRequest(event -> {
                logger.info("Cerrando aplicación...");
                // Aquí se pueden agregar tareas de limpieza si es necesario
                System.exit(0);
            });
            
            // Mostrar la ventana
            primaryStage.show();
            
            logger.info("Aplicación JavaFX iniciada exitosamente");
            
        } catch (Exception e) {
            logger.error("Error al iniciar la aplicación", e);
            System.exit(1);
        }
    }
    
    public static void main(String[] args) {
        logger.info("Iniciando MT5 Trading App...");
        launch(args);
    }
}
