@echo off
echo ========================================
echo    MT5 JavaFX Trading App
echo ========================================
echo.

echo Verificando dependencias...
python --version
if %errorlevel% neq 0 (
    echo Error: Python no encontrado
    pause
    exit /b 1
)

echo Verificando Maven...
mvn --version
if %errorlevel% neq 0 (
    echo Error: Maven no encontrado
    pause
    exit /b 1
)

echo.
echo Compilando aplicacion...
mvn clean compile
if %errorlevel% neq 0 (
    echo Error en la compilacion
    pause
    exit /b 1
)

echo.
echo ========================================
echo INSTRUCCIONES:
echo 1. Abre MetaTrader 5
echo 2. Conecta a tu broker
echo 3. La aplicacion se abrira automaticamente
echo ========================================
echo.
pause

echo Ejecutando aplicacion JavaFX...
mvn javafx:run
