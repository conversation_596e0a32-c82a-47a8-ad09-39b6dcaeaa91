<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.trading.app.controller.ChartController">
   <children>
      <!-- <PERSON><PERSON> de herramientas del gráfico -->
      <ToolBar>
         <items>
            <Label text="Símbolo:" />
            <ComboBox fx:id="chartSymbolComboBox" prefWidth="100.0" />
            <Separator orientation="VERTICAL" />
            
            <Label text="Timeframe:" />
            <ComboBox fx:id="chartTimeframeComboBox" prefWidth="80.0" />
            <Separator orientation="VERTICAL" />
            
            <Label text="Velas:" />
            <Spinner fx:id="candleCountSpinner" prefWidth="80.0" />
            <Separator orientation="VERTICAL" />
            
            <Button fx:id="loadChartButton" mnemonicParsing="false" onAction="#loadChart" text="Cargar" />
            <Button fx:id="startRealtimeButton" mnemonicParsing="false" onAction="#startRealtime" text="▶ Tiempo Real" />
            <Button fx:id="stopRealtimeButton" mnemonicParsing="false" onAction="#stopRealtime" text="⏸ Detener" />
            <Separator orientation="VERTICAL" />
            
            <CheckBox fx:id="autoScrollCheckBox" mnemonicParsing="false" text="Auto-scroll" />
         </items>
      </ToolBar>
      
      <!-- Área de estado -->
      <HBox alignment="CENTER_LEFT" spacing="10.0">
         <children>
            <Label text="Estado:" />
            <Label fx:id="chartStatusLabel" text="Gráfico listo" textFill="#666666" />
         </children>
         <padding>
            <Insets bottom="5.0" left="10.0" right="10.0" top="5.0" />
         </padding>
      </HBox>
      
      <!-- Contenedor del gráfico -->
      <VBox fx:id="chartContainer" VBox.vgrow="ALWAYS">
         <padding>
            <Insets bottom="10.0" left="10.0" right="10.0" top="5.0" />
         </padding>
      </VBox>
   </children>
</VBox>
