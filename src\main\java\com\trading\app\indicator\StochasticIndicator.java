package com.trading.app.indicator;

import com.trading.app.model.Candlestick;
import javafx.scene.canvas.GraphicsContext;
import javafx.scene.paint.Color;

import java.util.List;

/**
 * Indicador Oscilador Estocástico
 */
public class StochasticIndicator extends TechnicalIndicator {
    private int kPeriod;
    private int dPeriod;
    private double[] kPercent;
    private double[] dPercent;

    public StochasticIndicator() {
        this(14, 3);
    }

    public StochasticIndicator(int kPeriod, int dPeriod) {
        super("Stochastic", Color.BLUE, kPeriod);
        this.kPeriod = kPeriod;
        this.dPeriod = dPeriod;
    }

    @Override
    public double[] calculate(List<Candlestick> candlesticks) {
        if (candlesticks == null || candlesticks.size() < kPeriod) {
            return new double[0];
        }

        int size = candlesticks.size();
        kPercent = new double[size];
        dPercent = new double[size];
        
        // Calcular %K
        for (int i = 0; i < size; i++) {
            if (i < kPeriod - 1) {
                kPercent[i] = Double.NaN;
            } else {
                // Encontrar el máximo y mínimo en el período
                double highest = Double.MIN_VALUE;
                double lowest = Double.MAX_VALUE;
                
                for (int j = i - kPeriod + 1; j <= i; j++) {
                    highest = Math.max(highest, candlesticks.get(j).getHigh());
                    lowest = Math.min(lowest, candlesticks.get(j).getLow());
                }
                
                double currentClose = candlesticks.get(i).getClose();
                
                if (highest == lowest) {
                    kPercent[i] = 50; // Evitar división por cero
                } else {
                    kPercent[i] = ((currentClose - lowest) / (highest - lowest)) * 100;
                }
            }
        }
        
        // Calcular %D (SMA de %K)
        for (int i = 0; i < size; i++) {
            if (i < kPeriod + dPeriod - 2) {
                dPercent[i] = Double.NaN;
            } else {
                double sum = 0;
                int count = 0;
                
                for (int j = i - dPeriod + 1; j <= i; j++) {
                    if (!Double.isNaN(kPercent[j])) {
                        sum += kPercent[j];
                        count++;
                    }
                }
                
                if (count > 0) {
                    dPercent[i] = sum / count;
                } else {
                    dPercent[i] = Double.NaN;
                }
            }
        }
        
        return kPercent; // Retorna %K
    }

    @Override
    public void draw(GraphicsContext gc, List<Candlestick> candlesticks, double[] values,
                     double chartWidth, double chartHeight, double marginLeft, double marginTop,
                     double minPrice, double maxPrice, double candleWidth, double zoomFactor, double panX) {
        
        if (!visible || values == null || values.length == 0) return;
        
        // Dibujar líneas de referencia (20 y 80)
        gc.setStroke(Color.LIGHTGRAY);
        gc.setLineWidth(0.5);
        
        double y20 = marginTop + ((100 - 20) / 100.0) * chartHeight;
        double y80 = marginTop + ((100 - 80) / 100.0) * chartHeight;
        
        gc.strokeLine(marginLeft, y20, marginLeft + chartWidth, y20);
        gc.strokeLine(marginLeft, y80, marginLeft + chartWidth, y80);
        
        // Etiquetas
        gc.setFill(Color.GRAY);
        gc.fillText("20", marginLeft - 20, y20 + 3);
        gc.fillText("80", marginLeft - 20, y80 + 3);
        
        // Dibujar %K
        if (kPercent != null) {
            gc.setStroke(Color.BLUE);
            gc.setLineWidth(1.5);
            drawLine(gc, candlesticks, kPercent, chartWidth, chartHeight, marginLeft, marginTop,
                    0, 100, candleWidth, zoomFactor, panX);
        }
        
        // Dibujar %D
        if (dPercent != null) {
            gc.setStroke(Color.RED);
            gc.setLineWidth(1.0);
            drawLine(gc, candlesticks, dPercent, chartWidth, chartHeight, marginLeft, marginTop,
                    0, 100, candleWidth, zoomFactor, panX);
        }
    }

    @Override
    public boolean isOverlay() {
        return false; // Se dibuja en subpanel
    }

    @Override
    public double[] getValueRange(double[] values) {
        return new double[]{0, 100}; // Estocástico siempre está entre 0 y 100
    }

    // Getters para acceder a las líneas individuales
    public double[] getKPercent() {
        return kPercent;
    }

    public double[] getDPercent() {
        return dPercent;
    }

    public int getKPeriod() {
        return kPeriod;
    }

    public void setKPeriod(int kPeriod) {
        this.kPeriod = kPeriod;
        this.period = kPeriod; // Actualizar el período base
    }

    public int getDPeriod() {
        return dPeriod;
    }

    public void setDPeriod(int dPeriod) {
        this.dPeriod = dPeriod;
    }

    @Override
    public String toString() {
        return name + " (" + kPeriod + ", " + dPeriod + ")";
    }
}
