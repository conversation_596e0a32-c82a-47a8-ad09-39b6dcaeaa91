# 📈 Indicadores Técnicos Implementados

## ✅ Indicadores Completados

### 1. **Media Móvil Simple (SMA)**
- **Archivo**: `MovingAverageIndicator.java`
- **Tipo**: Overlay (sobre el gráfico principal)
- **Color**: Azul
- **Período**: 20 (configurable)
- **Descripción**: Promedio aritmético de los precios de cierre en N períodos

### 2. **Media Móvil Exponencial (EMA)**
- **Archivo**: `ExponentialMovingAverageIndicator.java`
- **Tipo**: Overlay (sobre el gráfico principal)
- **Color**: Naranja
- **Período**: 20 (configurable)
- **Descripción**: Media móvil que da más peso a los precios recientes

### 3. **Bandas de Bollinger**
- **Archivo**: `BollingerBandsIndicator.java`
- **Tipo**: Overlay (sobre el gráfico principal)
- **Color**: Púrpura con relleno semitransparente
- **Parámetros**: 20 períodos, 2 desviaciones estándar
- **Descripción**: SMA con bandas superior e inferior basadas en desviación estándar

### 4. **RSI (Índice de Fuerza Relativa)**
- **Archivo**: `RSIIndicator.java`
- **Tipo**: Subpanel (ventana separada)
- **Color**: Púrpura
- **Período**: 14 (configurable)
- **Rango**: 0-100 con líneas de referencia en 30 y 70
- **Descripción**: Oscilador de momentum que mide velocidad y cambio de movimientos de precio

### 5. **MACD (Convergencia/Divergencia de Medias Móviles)**
- **Archivo**: `MACDIndicator.java`
- **Tipo**: Subpanel (ventana separada)
- **Colores**: Azul (MACD), Rojo (Señal), Verde/Rojo (Histograma)
- **Parámetros**: EMA rápida 12, EMA lenta 26, Señal 9
- **Descripción**: Indicador de tendencia que muestra la relación entre dos medias móviles

### 6. **Oscilador Estocástico**
- **Archivo**: `StochasticIndicator.java`
- **Tipo**: Subpanel (ventana separada)
- **Colores**: Azul (%K), Rojo (%D)
- **Parámetros**: %K 14 períodos, %D 3 períodos
- **Rango**: 0-100 con líneas de referencia en 20 y 80
- **Descripción**: Oscilador de momentum que compara el precio de cierre con el rango de precios

## 🏗️ Arquitectura Implementada

### Clase Base: `TechnicalIndicator.java`
- Clase abstracta que define la interfaz común para todos los indicadores
- Métodos principales:
  - `calculate()`: Calcula los valores del indicador
  - `draw()`: Dibuja el indicador en el gráfico
  - `isOverlay()`: Determina si se dibuja sobre el gráfico o en subpanel
  - `getValueRange()`: Obtiene el rango de valores para escalado

### Integración con el Gráfico: `CandlestickChart.java`
- Soporte para indicadores overlay y subpanel
- Actualización automática cuando cambian los datos
- Gestión de colores y estilos
- Zoom y pan compatibles con indicadores

### Controlador: `ChartController.java`
- Botones para activar/desactivar cada indicador
- Cambio visual de botones cuando están activos
- Mensajes de estado informativos
- Gestión de instancias de indicadores

## 🎮 Interfaz de Usuario

### Botones de Indicadores
- **SMA**: Activa/desactiva Media Móvil Simple
- **EMA**: Activa/desactiva Media Móvil Exponencial  
- **Bollinger**: Activa/desactiva Bandas de Bollinger
- **RSI**: Activa/desactiva RSI
- **MACD**: Activa/desactiva MACD
- **Stochastic**: Activa/desactiva Oscilador Estocástico

### Retroalimentación Visual
- Los botones cambian de color cuando están activos
- Mensajes de estado muestran qué indicador se agregó/removió
- Indicadores se dibujan con colores distintivos

## 🔧 Características Técnicas

### Cálculos Matemáticos
- **SMA**: Promedio aritmético simple
- **EMA**: Suavizado exponencial con factor 2/(n+1)
- **Bollinger**: SMA ± (desviación estándar × multiplicador)
- **RSI**: RS = Promedio ganancias / Promedio pérdidas, RSI = 100 - (100/(1+RS))
- **MACD**: EMA(12) - EMA(26), Señal = EMA(9) del MACD, Histograma = MACD - Señal
- **Estocástico**: %K = ((Cierre - Mínimo) / (Máximo - Mínimo)) × 100, %D = SMA(%K)

### Rendimiento
- Cálculos optimizados para tiempo real
- Actualización incremental de indicadores
- Gestión eficiente de memoria
- Dibujado solo de elementos visibles

### Configurabilidad
- Períodos ajustables para cada indicador
- Colores personalizables
- Parámetros específicos (ej: desviaciones de Bollinger)
- Activación/desactivación individual

## 📋 Instrucciones de Uso

1. **Ejecutar la aplicación**: `mvn javafx:run`
2. **Conectar a MT5**: Presionar "Conectar MT5"
3. **Ir al gráfico**: Pestaña "📈 Gráfico"
4. **Cargar datos**: Seleccionar símbolo, timeframe y presionar "Cargar"
5. **Activar indicadores**: Hacer clic en los botones de indicadores deseados
6. **Tiempo real**: Presionar "▶ Tiempo Real" para actualizaciones en vivo
7. **Navegación**: Usar zoom (rueda del mouse) y pan (arrastrar)

## 🎯 Próximas Mejoras Posibles

- [ ] Configuración de parámetros desde la interfaz
- [ ] Más indicadores (Williams %R, CCI, etc.)
- [ ] Alertas basadas en indicadores
- [ ] Exportación de datos de indicadores
- [ ] Plantillas de indicadores guardadas
- [ ] Indicadores personalizados por el usuario

## ✅ Estado del Proyecto

**COMPLETADO**: Todos los indicadores solicitados están implementados y funcionando:
- ✅ Media móvil (MA)
- ✅ Media móvil exponencial (EMA)  
- ✅ Oscilador estocástico
- ✅ Convergencia/divergencia de medias móviles (MACD)
- ✅ Bandas de Bollinger
- ✅ Índice de fuerza relativo (RSI)

La aplicación está lista para usar con todos los indicadores técnicos funcionando correctamente en tiempo real.
