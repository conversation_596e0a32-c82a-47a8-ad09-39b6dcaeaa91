package com.trading.app.controller;

import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.ResourceBundle;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.ConcurrentHashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.trading.app.model.HistoricalData;
import com.trading.app.model.Price;
import com.trading.app.service.MT5Service;

import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Button;
import javafx.scene.control.ComboBox;
import javafx.scene.control.Label;
import javafx.scene.control.Spinner;
import javafx.scene.control.SpinnerValueFactory;
import javafx.scene.control.TabPane;
import javafx.scene.control.TableCell;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import javafx.scene.control.TextArea;
import javafx.scene.control.cell.PropertyValueFactory;

/**
 * Controlador principal de la interfaz JavaFX
 */
public class MainController implements Initializable {
    private static final Logger logger = LoggerFactory.getLogger(MainController.class);

    // Componentes FXML
    @FXML private Button connectButton;
    @FXML private ComboBox<String> symbolComboBox;
    @FXML private Button addSymbolButton;
    @FXML private ComboBox<String> timeframeComboBox;
    @FXML private Button loadHistoryButton;
    @FXML private Label statusLabel;
    @FXML private Label connectionTimeLabel;
    @FXML private TabPane mainTabPane;

    // Tabla de precios en tiempo real
    @FXML private TableView<Price> pricesTable;
    @FXML private TableColumn<Price, String> symbolColumn;
    @FXML private TableColumn<Price, Double> bidColumn;
    @FXML private TableColumn<Price, Double> askColumn;
    @FXML private TableColumn<Price, Double> lastColumn;
    @FXML private TableColumn<Price, Double> spreadColumn;
    @FXML private TableColumn<Price, Long> volumeColumn;
    @FXML private TableColumn<Price, LocalDateTime> timeColumn;

    // Detalles del precio seleccionado
    @FXML private Label detailSymbolLabel;
    @FXML private Label detailBidLabel;
    @FXML private Label detailAskLabel;
    @FXML private Label detailSpreadLabel;

    // Tabla de datos históricos
    @FXML private TableView<HistoricalData> historicalTable;
    @FXML private TableColumn<HistoricalData, LocalDateTime> histTimeColumn;
    @FXML private TableColumn<HistoricalData, Double> histOpenColumn;
    @FXML private TableColumn<HistoricalData, Double> histHighColumn;
    @FXML private TableColumn<HistoricalData, Double> histLowColumn;
    @FXML private TableColumn<HistoricalData, Double> histCloseColumn;
    @FXML private TableColumn<HistoricalData, Long> histVolumeColumn;

    @FXML private Spinner<Integer> countSpinner;
    @FXML private Button refreshHistoryButton;
    @FXML private TextArea logTextArea;

    // Servicios y datos
    private MT5Service mt5Service;
    private ObservableList<Price> pricesList;
    private ObservableList<HistoricalData> historicalDataList;
    private Map<String, Task<Void>> priceStreamTasks;
    private Timer connectionTimer;
    private LocalDateTime connectionStartTime;
    private ChartController chartController;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        mt5Service = new MT5Service();
        pricesList = FXCollections.observableArrayList();
        historicalDataList = FXCollections.observableArrayList();
        priceStreamTasks = new ConcurrentHashMap<>();

        setupUI();
        setupTableColumns();
        setupEventHandlers();
        setupChartController();

        logMessage("Aplicación iniciada. Presiona 'Conectar MT5' para comenzar.");
    }

    private void setupUI() {
        // Configurar ComboBox de símbolos
        symbolComboBox.setItems(FXCollections.observableArrayList(
            "EURUSD", "GBPUSD", "USDJPY", "USDCHF", "AUDUSD", "USDCAD", "NZDUSD"
        ));
        symbolComboBox.setValue("EURUSD");

        // Configurar ComboBox de timeframes
        timeframeComboBox.setItems(FXCollections.observableArrayList(
            "M1", "M5", "M15", "M30", "H1", "H4", "D1", "W1", "MN1"
        ));
        timeframeComboBox.setValue("M15");

        // Configurar Spinner
        countSpinner.setValueFactory(new SpinnerValueFactory.IntegerSpinnerValueFactory(10, 1000, 100, 10));

        // Configurar tablas
        pricesTable.setItems(pricesList);
        historicalTable.setItems(historicalDataList);

        // Estado inicial
        updateConnectionStatus(false);
    }

    private void setupTableColumns() {
        // Columnas de precios en tiempo real
        symbolColumn.setCellValueFactory(new PropertyValueFactory<>("symbol"));
        bidColumn.setCellValueFactory(new PropertyValueFactory<>("bid"));
        askColumn.setCellValueFactory(new PropertyValueFactory<>("ask"));
        lastColumn.setCellValueFactory(new PropertyValueFactory<>("last"));
        spreadColumn.setCellValueFactory(new PropertyValueFactory<>("spread"));
        volumeColumn.setCellValueFactory(new PropertyValueFactory<>("volume"));
        timeColumn.setCellValueFactory(new PropertyValueFactory<>("timestamp"));

        // Formatear columnas numéricas
        bidColumn.setCellFactory(col -> new TableCell<Price, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(String.format("%.5f", item));
                }
            }
        });

        askColumn.setCellFactory(col -> new TableCell<Price, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(String.format("%.5f", item));
                }
            }
        });

        lastColumn.setCellFactory(col -> new TableCell<Price, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(String.format("%.5f", item));
                }
            }
        });

        spreadColumn.setCellFactory(col -> new TableCell<Price, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(String.format("%.1f", item * 10000)); // En pips
                }
            }
        });

        timeColumn.setCellFactory(col -> new TableCell<Price, LocalDateTime>() {
            @Override
            protected void updateItem(LocalDateTime item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(item.format(DateTimeFormatter.ofPattern("HH:mm:ss")));
                }
            }
        });

        // Columnas de datos históricos
        histTimeColumn.setCellValueFactory(new PropertyValueFactory<>("timestamp"));
        histOpenColumn.setCellValueFactory(new PropertyValueFactory<>("open"));
        histHighColumn.setCellValueFactory(new PropertyValueFactory<>("high"));
        histLowColumn.setCellValueFactory(new PropertyValueFactory<>("low"));
        histCloseColumn.setCellValueFactory(new PropertyValueFactory<>("close"));
        histVolumeColumn.setCellValueFactory(new PropertyValueFactory<>("volume"));

        // Formatear columnas de datos históricos
        histTimeColumn.setCellFactory(col -> new TableCell<HistoricalData, LocalDateTime>() {
            @Override
            protected void updateItem(LocalDateTime item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(item.format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")));
                }
            }
        });

        for (TableColumn<HistoricalData, Double> column : Arrays.asList(histOpenColumn, histHighColumn, histLowColumn, histCloseColumn)) {
            column.setCellFactory(col -> new TableCell<HistoricalData, Double>() {
                @Override
                protected void updateItem(Double item, boolean empty) {
                    super.updateItem(item, empty);
                    if (empty || item == null) {
                        setText(null);
                    } else {
                        setText(String.format("%.5f", item));
                    }
                }
            });
        }
    }

    private void setupEventHandlers() {
        // Selección en tabla de precios
        pricesTable.getSelectionModel().selectedItemProperty().addListener((obs, oldSelection, newSelection) -> {
            if (newSelection != null) {
                updatePriceDetails(newSelection);
            }
        });
    }

    private void setupChartController() {
        // Usar Platform.runLater para asegurar que el TabPane esté completamente cargado
        Platform.runLater(() -> {
            try {
                // Buscar la pestaña del gráfico
                if (mainTabPane != null && mainTabPane.getTabs().size() > 1) {
                    // La pestaña del gráfico es la segunda (índice 1)
                    var chartTab = mainTabPane.getTabs().get(1);
                    if (chartTab.getContent() != null) {
                        // Buscar el ChartController en el contenido de la pestaña
                        // Esto requiere que el FXML se haya cargado completamente
                        logger.info("Pestaña de gráfico encontrada, configurando ChartController...");
                        // El ChartController se configurará cuando se necesite
                    }
                }
            } catch (Exception e) {
                logger.error("Error configurando ChartController", e);
            }
        });
    }

    @FXML
    private void connectToMT5() {
        if (mt5Service.isConnected()) {
            disconnect();
        } else {
            connect();
        }
    }

    private void connect() {
        connectButton.setDisable(true);
        logMessage("Conectando a MetaTrader 5...");

        mt5Service.initialize().thenAccept(success -> {
            Platform.runLater(() -> {
                if (success) {
                    updateConnectionStatus(true);
                    logMessage("Conexión establecida exitosamente");
                    connectionStartTime = LocalDateTime.now();
                    startConnectionTimer();
                } else {
                    updateConnectionStatus(false);
                    logMessage("Error: No se pudo conectar a MetaTrader 5");
                }
                connectButton.setDisable(false);
            });
        }).exceptionally(throwable -> {
            Platform.runLater(() -> {
                updateConnectionStatus(false);
                logMessage("Error de conexión: " + throwable.getMessage());
                connectButton.setDisable(false);
            });
            return null;
        });
    }

    private void disconnect() {
        // Detener todos los streams de precios
        priceStreamTasks.values().forEach(task -> task.cancel());
        priceStreamTasks.clear();

        // Detener timer de conexión
        if (connectionTimer != null) {
            connectionTimer.cancel();
        }

        // Cerrar servicio MT5
        mt5Service.shutdown();

        updateConnectionStatus(false);
        logMessage("Desconectado de MetaTrader 5");
    }

    @FXML
    private void addSymbol() {
        String symbol = symbolComboBox.getValue();
        if (symbol != null && !symbol.trim().isEmpty() && mt5Service.isConnected()) {
            startPriceStream(symbol.trim().toUpperCase());
        }
    }

    @FXML
    private void loadHistoricalData() {
        String symbol = symbolComboBox.getValue();
        String timeframe = timeframeComboBox.getValue();
        int count = countSpinner.getValue();

        if (symbol != null && timeframe != null && mt5Service.isConnected()) {
            loadHistoricalData(symbol, timeframe, count);
        }
    }

    @FXML
    private void refreshHistoricalData() {
        loadHistoricalData();
    }

    private void startPriceStream(String symbol) {
        // Cancelar stream anterior si existe
        Task<Void> existingTask = priceStreamTasks.get(symbol);
        if (existingTask != null) {
            existingTask.cancel();
        }

        Task<Void> streamTask = mt5Service.startPriceStream(symbol, price -> {
            Platform.runLater(() -> {
                updatePriceInTable(price);
            });
        });

        priceStreamTasks.put(symbol, streamTask);

        Thread streamThread = new Thread(streamTask);
        streamThread.setDaemon(true);
        streamThread.start();

        logMessage("Iniciado streaming de precios para " + symbol);
    }

    private void updatePriceInTable(Price newPrice) {
        // Buscar si ya existe el símbolo en la tabla
        Optional<Price> existingPrice = pricesList.stream()
            .filter(p -> p.getSymbol().equals(newPrice.getSymbol()))
            .findFirst();

        if (existingPrice.isPresent()) {
            // Actualizar precio existente
            Price existing = existingPrice.get();
            existing.setBid(newPrice.getBid());
            existing.setAsk(newPrice.getAsk());
            existing.setLast(newPrice.getLast());
            existing.setVolume(newPrice.getVolume());
            existing.setTimestamp(newPrice.getTimestamp());
            pricesTable.refresh();
        } else {
            // Agregar nuevo precio
            pricesList.add(newPrice);
        }

        // Actualizar detalles si este símbolo está seleccionado
        Price selectedPrice = pricesTable.getSelectionModel().getSelectedItem();
        if (selectedPrice != null && selectedPrice.getSymbol().equals(newPrice.getSymbol())) {
            updatePriceDetails(newPrice);
        }
    }

    private void loadHistoricalData(String symbol, String timeframe, int count) {
        logMessage(String.format("Cargando datos históricos para %s (%s, %d barras)...", symbol, timeframe, count));

        mt5Service.getHistoricalData(symbol, timeframe, count).thenAccept(data -> {
            Platform.runLater(() -> {
                historicalDataList.clear();
                historicalDataList.addAll(data);
                logMessage(String.format("Cargados %d registros históricos para %s", data.size(), symbol));
            });
        }).exceptionally(throwable -> {
            Platform.runLater(() -> {
                logMessage("Error al cargar datos históricos: " + throwable.getMessage());
            });
            return null;
        });
    }

    private void updatePriceDetails(Price price) {
        detailSymbolLabel.setText(price.getSymbol());
        detailBidLabel.setText(String.format("%.5f", price.getBid()));
        detailAskLabel.setText(String.format("%.5f", price.getAsk()));
        detailSpreadLabel.setText(String.format("%.1f pips", price.getSpread() * 10000));
    }

    private void updateConnectionStatus(boolean connected) {
        if (connected) {
            statusLabel.setText("Conectado");
            statusLabel.setStyle("-fx-text-fill: green;");
            connectButton.setText("Desconectar");
            addSymbolButton.setDisable(false);
            loadHistoryButton.setDisable(false);
            refreshHistoryButton.setDisable(false);

            // Configurar ChartController si está disponible
            setupChartControllerConnection();
        } else {
            statusLabel.setText("Desconectado");
            statusLabel.setStyle("-fx-text-fill: red;");
            connectButton.setText("Conectar MT5");
            connectionTimeLabel.setText("--:--:--");
            addSymbolButton.setDisable(true);
            loadHistoryButton.setDisable(true);
            refreshHistoryButton.setDisable(true);

            // Limpiar ChartController si está disponible
            if (chartController != null) {
                chartController.setMT5Service(null);
                chartController.updateButtonStates();
            }
        }
    }

    private void setupChartControllerConnection() {
        // Usar la instancia estática del ChartController
        Platform.runLater(() -> {
            try {
                chartController = ChartController.getInstance();
                if (chartController != null) {
                    chartController.setMT5Service(mt5Service);
                    chartController.updateButtonStates();
                    logger.info("ChartController configurado exitosamente");
                } else {
                    logger.info("ChartController aún no está disponible");
                }
            } catch (Exception e) {
                logger.error("Error configurando conexión del ChartController", e);
            }
        });
    }

    private void startConnectionTimer() {
        connectionTimer = new Timer(true);
        connectionTimer.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                if (connectionStartTime != null) {
                    Platform.runLater(() -> {
                        LocalDateTime now = LocalDateTime.now();
                        long seconds = java.time.Duration.between(connectionStartTime, now).getSeconds();
                        long hours = seconds / 3600;
                        long minutes = (seconds % 3600) / 60;
                        long secs = seconds % 60;
                        connectionTimeLabel.setText(String.format("%02d:%02d:%02d", hours, minutes, secs));
                    });
                }
            }
        }, 1000, 1000);
    }

    private void logMessage(String message) {
        Platform.runLater(() -> {
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
            logTextArea.appendText(String.format("[%s] %s%n", timestamp, message));
        });
        logger.info(message);
    }
}
