package com.trading.app.model;

import java.time.LocalDateTime;

/**
 * Modelo para representar datos históric<PERSON> (OHLCV)
 */
public class HistoricalData {
    private String symbol;
    private LocalDateTime timestamp;
    private double open;
    private double high;
    private double low;
    private double close;
    private long volume;
    private String timeframe;

    public HistoricalData() {
    }

    public HistoricalData(String symbol, LocalDateTime timestamp, double open, double high, 
                         double low, double close, long volume, String timeframe) {
        this.symbol = symbol;
        this.timestamp = timestamp;
        this.open = open;
        this.high = high;
        this.low = low;
        this.close = close;
        this.volume = volume;
        this.timeframe = timeframe;
    }

    // Getters and Setters
    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public double getOpen() {
        return open;
    }

    public void setOpen(double open) {
        this.open = open;
    }

    public double getHigh() {
        return high;
    }

    public void setHigh(double high) {
        this.high = high;
    }

    public double getLow() {
        return low;
    }

    public void setLow(double low) {
        this.low = low;
    }

    public double getClose() {
        return close;
    }

    public void setClose(double close) {
        this.close = close;
    }

    public long getVolume() {
        return volume;
    }

    public void setVolume(long volume) {
        this.volume = volume;
    }

    public String getTimeframe() {
        return timeframe;
    }

    public void setTimeframe(String timeframe) {
        this.timeframe = timeframe;
    }

    @Override
    public String toString() {
        return String.format("HistoricalData{symbol='%s', timestamp=%s, open=%.5f, high=%.5f, low=%.5f, close=%.5f, volume=%d, timeframe='%s'}",
                symbol, timestamp, open, high, low, close, volume, timeframe);
    }
}
