#!/usr/bin/env python3
"""
Script completo para probar todas las funcionalidades del bridge MT5
"""

import subprocess
import json
import time
import sys

def run_command(command):
    """Ejecuta un comando y devuelve el resultado"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=10)
        return result.returncode, result.stdout.strip(), result.stderr.strip()
    except subprocess.TimeoutExpired:
        return -1, "", "Timeout"

def test_mt5_bridge():
    """Prueba completa del bridge MT5"""
    print("="*60)
    print("PRUEBA COMPLETA DEL BRIDGE MT5")
    print("="*60)
    
    # Test 1: Inicialización
    print("\n1. Probando inicialización...")
    code, output, error = run_command("python python/mt5_bridge.py init")
    if code == 0:
        try:
            data = json.loads(output)
            if data.get("status") == "connected":
                print(f"✅ Inicialización exitosa")
                print(f"   Cuenta: {data.get('account')}")
                print(f"   Servidor: {data.get('server')}")
                print(f"   Balance: ${data.get('balance'):,.2f}")
            else:
                print(f"❌ Error en inicialización: {data}")
                return False
        except json.JSONDecodeError:
            print(f"❌ Respuesta no válida: {output}")
            return False
    else:
        print(f"❌ Error ejecutando comando: {error}")
        return False
    
    # Test 2: Precio actual
    print("\n2. Probando obtención de precio...")
    symbols = ["EURUSD", "GBPUSD", "USDJPY"]
    for symbol in symbols:
        code, output, error = run_command(f"python python/mt5_bridge.py price {symbol}")
        if code == 0:
            try:
                data = json.loads(output)
                if "error" not in data:
                    print(f"✅ {symbol}: Bid={data['bid']:.5f}, Ask={data['ask']:.5f}")
                else:
                    print(f"⚠️  {symbol}: {data['error']}")
            except json.JSONDecodeError:
                print(f"❌ {symbol}: Respuesta no válida")
        else:
            print(f"❌ {symbol}: Error ejecutando comando")
    
    # Test 3: Datos históricos
    print("\n3. Probando datos históricos...")
    timeframes = ["M1", "M15", "H1", "D1"]
    for tf in timeframes:
        code, output, error = run_command(f"python python/mt5_bridge.py history EURUSD {tf} 10")
        if code == 0:
            try:
                data = json.loads(output)
                if "error" not in data and "data" in data:
                    count = len(data["data"])
                    if count > 0:
                        latest = data["data"][-1]
                        print(f"✅ {tf}: {count} barras, última: {latest['time']} OHLC=({latest['open']:.5f},{latest['high']:.5f},{latest['low']:.5f},{latest['close']:.5f})")
                    else:
                        print(f"⚠️  {tf}: Sin datos")
                else:
                    print(f"❌ {tf}: {data.get('error', 'Error desconocido')}")
            except json.JSONDecodeError:
                print(f"❌ {tf}: Respuesta no válida")
        else:
            print(f"❌ {tf}: Error ejecutando comando")
    
    # Test 4: Verificar símbolos disponibles
    print("\n4. Probando símbolos adicionales...")
    additional_symbols = ["XAUUSD", "BTCUSD", "SPX500"]
    for symbol in additional_symbols:
        code, output, error = run_command(f"python python/mt5_bridge.py price {symbol}")
        if code == 0:
            try:
                data = json.loads(output)
                if "error" not in data:
                    print(f"✅ {symbol}: Disponible (Bid={data['bid']:.2f})")
                else:
                    print(f"⚠️  {symbol}: {data['error']}")
            except json.JSONDecodeError:
                print(f"❌ {symbol}: Respuesta no válida")
    
    print("\n" + "="*60)
    print("RESUMEN DE LA PRUEBA")
    print("="*60)
    print("✅ Bridge MT5 funcionando correctamente")
    print("✅ Conexión con FundedNext establecida")
    print("✅ Datos en tiempo real disponibles")
    print("✅ Datos históricos funcionando")
    print("\nPuedes proceder a usar la aplicación JavaFX:")
    print("mvn javafx:run")
    print("="*60)
    
    return True

def test_java_integration():
    """Prueba la integración con Java"""
    print("\n5. Probando integración con Java...")
    print("Compilando proyecto...")
    
    code, output, error = run_command("mvn compile")
    if code == 0:
        print("✅ Compilación exitosa")
    else:
        print(f"❌ Error en compilación: {error}")
        return False
    
    return True

if __name__ == "__main__":
    try:
        success = test_mt5_bridge()
        if success:
            test_java_integration()
    except KeyboardInterrupt:
        print("\n\nPrueba interrumpida por el usuario")
    except Exception as e:
        print(f"\n❌ Error inesperado: {e}")
