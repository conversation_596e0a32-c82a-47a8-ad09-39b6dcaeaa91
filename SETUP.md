# 🚀 Configuración e Instalación - MT5 JavaFX Trading App

## ✅ Estado Actual
- ✅ **Proyecto compilado exitosamente**
- ✅ **Dependencias Python instaladas**
- ✅ **Aplicación JavaFX ejecutándose**

## 📋 Requisitos Verificados
- ✅ Java 21 (instalado)
- ✅ Maven 3.9.9 (instalado)
- ✅ Python 3.13 (instalado)
- ✅ MetaTrader5 library (instalada)

## 🎯 Pasos para Usar la Aplicación

### 1. Preparar MetaTrader 5
```bash
# Abre MetaTrader 5
# Conecta a tu broker
# Asegúrate de que esté funcionando correctamente
```

### 2. Ejecutar la Aplicación
```bash
# Opción 1: Usar el script batch
run.bat

# Opción 2: Comando Maven directo
mvn javafx:run

# Opción 3: Compilar y ejecutar paso a paso
mvn clean compile
mvn javafx:run
```

### 3. Usar la Interfaz
1. **Conectar**: <PERSON>siona "Conectar MT5" en la aplicación
2. **Ag<PERSON>gar <PERSON>**: Selecciona un símbolo (ej: EURUSD) y presiona "Agregar"
3. **Ver Datos Históricos**: Selecciona timeframe y presiona "Cargar Histórico"

## 🔧 Funcionalidades Disponibles

### ✅ Implementadas
- **Conexión con MT5** via Python bridge
- **Precios en tiempo real** para múltiples símbolos
- **Datos históricos** con diferentes timeframes
- **Interfaz JavaFX** moderna y responsive
- **Log de actividades** en tiempo real
- **Panel de detalles** para precios seleccionados

### 📊 Símbolos Soportados
- EURUSD, GBPUSD, USDJPY, USDCHF
- AUDUSD, USDCAD, NZDUSD
- Cualquier símbolo disponible en tu broker

### ⏰ Timeframes Disponibles
- M1, M5, M15, M30 (minutos)
- H1, H4 (horas)
- D1 (diario), W1 (semanal), MN1 (mensual)

## 🐛 Solución de Problemas

### Error de Conexión MT5
```bash
# Verificar conexión
python test_mt5_connection.py

# Soluciones:
# 1. Abre MetaTrader 5
# 2. Conecta a tu broker
# 3. Verifica permisos de Expert Advisors
```

### Error de Compilación Java
```bash
# Verificar Java
java --version

# Recompilar
mvn clean compile
```

### Error de Dependencias Python
```bash
# Reinstalar dependencias
pip install -r requirements.txt
```

## 📁 Estructura del Proyecto
```
Trading/
├── src/main/java/com/trading/app/
│   ├── TradingApp.java              # Aplicación principal
│   ├── controller/MainController.java # Controlador UI
│   ├── model/                       # Modelos de datos
│   └── service/MT5Service.java      # Servicio MT5
├── src/main/resources/fxml/
│   └── main.fxml                    # Interfaz FXML
├── python/
│   └── mt5_bridge.py               # Bridge Python-MT5
├── test_mt5_connection.py          # Script de prueba
├── run.bat                         # Script de ejecución
└── pom.xml                         # Configuración Maven
```

## 🎉 ¡Listo para Usar!

La aplicación está completamente funcional. Solo necesitas:
1. Abrir MetaTrader 5
2. Ejecutar `mvn javafx:run` o `run.bat`
3. ¡Comenzar a hacer trading!

## 📞 Soporte
Si encuentras algún problema, revisa:
1. Los logs en la aplicación
2. El archivo `test_mt5_connection.py`
3. La documentación en `README.md`
