package com.trading.app.model;

import java.time.LocalDateTime;

/**
 * Modelo para representar un precio en tiempo real
 */
public class Price {
    private String symbol;
    private double bid;
    private double ask;
    private double last;
    private long volume;
    private LocalDateTime timestamp;
    private double spread;

    public Price() {
        this.timestamp = LocalDateTime.now();
    }

    public Price(String symbol, double bid, double ask, double last, long volume) {
        this.symbol = symbol;
        this.bid = bid;
        this.ask = ask;
        this.last = last;
        this.volume = volume;
        this.timestamp = LocalDateTime.now();
        this.spread = ask - bid;
    }

    // Getters and Setters
    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    public double getBid() {
        return bid;
    }

    public void setBid(double bid) {
        this.bid = bid;
        updateSpread();
    }

    public double getAsk() {
        return ask;
    }

    public void setAsk(double ask) {
        this.ask = ask;
        updateSpread();
    }

    public double getLast() {
        return last;
    }

    public void setLast(double last) {
        this.last = last;
    }

    public long getVolume() {
        return volume;
    }

    public void setVolume(long volume) {
        this.volume = volume;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public double getSpread() {
        return spread;
    }

    private void updateSpread() {
        this.spread = ask - bid;
    }

    @Override
    public String toString() {
        return String.format("Price{symbol='%s', bid=%.5f, ask=%.5f, last=%.5f, volume=%d, spread=%.5f, timestamp=%s}",
                symbol, bid, ask, last, volume, spread, timestamp);
    }
}
