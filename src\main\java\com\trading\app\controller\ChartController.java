package com.trading.app.controller;

import java.net.URL;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.ResourceBundle;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.trading.app.component.CandlestickChart;
import com.trading.app.model.Candlestick;
import com.trading.app.model.HistoricalData;
import com.trading.app.model.Price;
import com.trading.app.service.MT5Service;

import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Button;
import javafx.scene.control.CheckBox;
import javafx.scene.control.ComboBox;
import javafx.scene.control.Label;
import javafx.scene.control.Spinner;
import javafx.scene.control.SpinnerValueFactory;
import javafx.scene.layout.VBox;

/**
 * Controlador para el gráfico de velas en tiempo real
 */
public class ChartController implements Initializable {
    private static final Logger logger = LoggerFactory.getLogger(ChartController.class);
    private static ChartController instance;

    @FXML private VBox chartContainer;
    @FXML private ComboBox<String> chartSymbolComboBox;
    @FXML private ComboBox<String> chartTimeframeComboBox;
    @FXML private Button loadChartButton;
    @FXML private Button startRealtimeButton;
    @FXML private Button stopRealtimeButton;
    @FXML private Label chartStatusLabel;
    @FXML private Spinner<Integer> candleCountSpinner;
    @FXML private CheckBox autoScrollCheckBox;

    private CandlestickChart candlestickChart;
    private MT5Service mt5Service;
    private ObservableList<Candlestick> chartData;
    private Task<Void> realtimeTask;
    private boolean isRealtimeActive = false;
    
    // Para manejar velas en tiempo real
    private Candlestick currentCandle;
    private String currentSymbol;
    private String currentTimeframe;
    private LocalDateTime lastCandleTime;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        instance = this; // Establecer la instancia estática
        setupUI();
        setupChart();
        setupEventHandlers();
    }

    /**
     * Obtiene la instancia del ChartController
     */
    public static ChartController getInstance() {
        return instance;
    }

    private void setupUI() {
        // Configurar ComboBox de símbolos
        chartSymbolComboBox.setItems(FXCollections.observableArrayList(
            "EURUSD", "GBPUSD", "USDJPY", "USDCHF", "AUDUSD", "USDCAD", "NZDUSD",
            "XAUUSD", "XAGUSD", "BTCUSD", "SPX500", "NAS100", "GER40"
        ));
        chartSymbolComboBox.setValue("EURUSD");

        // Configurar ComboBox de timeframes
        chartTimeframeComboBox.setItems(FXCollections.observableArrayList(
            "M1", "M5", "M15", "M30", "H1", "H4", "D1"
        ));
        chartTimeframeComboBox.setValue("M15");

        // Configurar Spinner para cantidad de velas
        candleCountSpinner.setValueFactory(
            new SpinnerValueFactory.IntegerSpinnerValueFactory(50, 1000, 200, 50)
        );

        // Auto-scroll activado por defecto
        autoScrollCheckBox.setSelected(true);

        // Estado inicial de botones
        startRealtimeButton.setDisable(true);
        stopRealtimeButton.setDisable(true);
        loadChartButton.setDisable(true);
    }

    private void setupChart() {
        candlestickChart = new CandlestickChart();
        chartData = FXCollections.observableArrayList();
        
        // Configurar el gráfico para que ocupe todo el espacio disponible
        chartContainer.getChildren().clear();
        chartContainer.getChildren().add(candlestickChart);
        
        // Hacer que el gráfico se expanda
        VBox.setVgrow(candlestickChart, javafx.scene.layout.Priority.ALWAYS);
        
        updateChartStatus("Gráfico listo. Conecta a MT5 para cargar datos.");
    }

    private void setupEventHandlers() {
        // Cambio de símbolo o timeframe
        chartSymbolComboBox.setOnAction(e -> {
            if (isRealtimeActive) {
                stopRealtime();
            }
        });

        chartTimeframeComboBox.setOnAction(e -> {
            if (isRealtimeActive) {
                stopRealtime();
            }
        });
    }

    /**
     * Establece el servicio MT5 (llamado desde el controlador principal)
     */
    public void setMT5Service(MT5Service mt5Service) {
        this.mt5Service = mt5Service;
        updateButtonStates();
    }

    /**
     * Actualiza el estado de los botones según la conexión
     */
    public void updateButtonStates() {
        boolean connected = mt5Service != null && mt5Service.isConnected();
        loadChartButton.setDisable(!connected);
        startRealtimeButton.setDisable(!connected || isRealtimeActive);
        stopRealtimeButton.setDisable(!isRealtimeActive);
    }

    @FXML
    private void loadChart() {
        String symbol = chartSymbolComboBox.getValue();
        String timeframe = chartTimeframeComboBox.getValue();
        int count = candleCountSpinner.getValue();

        if (symbol == null || timeframe == null || mt5Service == null) {
            updateChartStatus("Error: Parámetros no válidos");
            return;
        }

        updateChartStatus("Cargando datos históricos...");
        loadChartButton.setDisable(true);

        mt5Service.getHistoricalData(symbol, timeframe, count).thenAccept(historicalData -> {
            Platform.runLater(() -> {
                try {
                    List<Candlestick> candlesticks = new ArrayList<>();
                    for (HistoricalData data : historicalData) {
                        candlesticks.add(Candlestick.fromHistoricalData(data));
                    }

                    chartData.clear();
                    chartData.addAll(candlesticks);
                    candlestickChart.setCandlesticks(candlesticks);

                    currentSymbol = symbol;
                    currentTimeframe = timeframe;
                    
                    updateChartStatus(String.format("Cargadas %d velas para %s (%s)", 
                        candlesticks.size(), symbol, timeframe));
                    
                } catch (Exception e) {
                    logger.error("Error procesando datos históricos", e);
                    updateChartStatus("Error al procesar datos históricos");
                } finally {
                    loadChartButton.setDisable(false);
                    updateButtonStates();
                }
            });
        }).exceptionally(throwable -> {
            Platform.runLater(() -> {
                logger.error("Error cargando datos históricos", throwable);
                updateChartStatus("Error: " + throwable.getMessage());
                loadChartButton.setDisable(false);
                updateButtonStates();
            });
            return null;
        });
    }

    @FXML
    private void startRealtime() {
        if (chartData.isEmpty()) {
            updateChartStatus("Primero carga datos históricos");
            return;
        }

        String symbol = chartSymbolComboBox.getValue();
        if (symbol == null || mt5Service == null) {
            updateChartStatus("Error: No se puede iniciar tiempo real");
            return;
        }

        isRealtimeActive = true;
        updateButtonStates();
        updateChartStatus("Iniciando datos en tiempo real...");

        // Inicializar la vela actual
        currentCandle = null;
        lastCandleTime = null;

        // Crear tarea para el streaming de precios
        realtimeTask = mt5Service.startPriceStream(symbol, this::handleRealtimePrice);

        Thread realtimeThread = new Thread(realtimeTask);
        realtimeThread.setDaemon(true);
        realtimeThread.start();

        updateChartStatus("Datos en tiempo real activos para " + symbol);
    }

    @FXML
    private void stopRealtime() {
        if (realtimeTask != null) {
            realtimeTask.cancel();
            realtimeTask = null;
        }

        isRealtimeActive = false;
        updateButtonStates();
        updateChartStatus("Datos en tiempo real detenidos");
    }

    private void handleRealtimePrice(Price price) {
        Platform.runLater(() -> {
            try {
                LocalDateTime now = LocalDateTime.now();
                LocalDateTime candleTime = getCandleStartTime(now, currentTimeframe);

                // Si es una nueva vela
                if (currentCandle == null || !candleTime.equals(lastCandleTime)) {
                    // Completar la vela anterior si existe
                    if (currentCandle != null) {
                        currentCandle.setComplete(true);
                    }

                    // Crear nueva vela
                    currentCandle = new Candlestick();
                    currentCandle.setSymbol(price.getSymbol());
                    currentCandle.setTimestamp(candleTime);
                    currentCandle.setTimeframe(currentTimeframe);
                    currentCandle.updateWithPrice(price.getBid()); // Usar bid como precio

                    chartData.add(currentCandle);
                    candlestickChart.addCandlestick(currentCandle);

                    lastCandleTime = candleTime;

                    // Auto-scroll si está habilitado
                    if (autoScrollCheckBox.isSelected()) {
                        // Mantener solo las últimas N velas para rendimiento
                        int maxCandles = candleCountSpinner.getValue();
                        while (chartData.size() > maxCandles) {
                            chartData.remove(0);
                        }
                        candlestickChart.setCandlesticks(new ArrayList<>(chartData));
                    }
                } else {
                    // Actualizar vela actual
                    currentCandle.updateWithPrice(price.getBid());
                    candlestickChart.updateLastCandlestick(currentCandle);
                }

            } catch (Exception e) {
                logger.error("Error procesando precio en tiempo real", e);
            }
        });
    }

    /**
     * Calcula el tiempo de inicio de la vela según el timeframe
     */
    private LocalDateTime getCandleStartTime(LocalDateTime time, String timeframe) {
        LocalDateTime truncated = time.truncatedTo(ChronoUnit.MINUTES);
        
        switch (timeframe) {
            case "M1":
                return truncated;
            case "M5":
                return truncated.withMinute((truncated.getMinute() / 5) * 5);
            case "M15":
                return truncated.withMinute((truncated.getMinute() / 15) * 15);
            case "M30":
                return truncated.withMinute((truncated.getMinute() / 30) * 30);
            case "H1":
                return truncated.truncatedTo(ChronoUnit.HOURS);
            case "H4":
                return truncated.truncatedTo(ChronoUnit.HOURS)
                    .withHour((truncated.getHour() / 4) * 4);
            case "D1":
                return truncated.truncatedTo(ChronoUnit.DAYS);
            default:
                return truncated;
        }
    }

    private void updateChartStatus(String message) {
        if (chartStatusLabel != null) {
            chartStatusLabel.setText(message);
        }
        logger.info("Chart: " + message);
    }

    /**
     * Limpia el gráfico
     */
    public void clearChart() {
        chartData.clear();
        candlestickChart.setCandlesticks(new ArrayList<>());
        stopRealtime();
        updateChartStatus("Gráfico limpiado");
    }

    /**
     * Método llamado cuando se cierra la aplicación
     */
    public void shutdown() {
        stopRealtime();
    }
}
