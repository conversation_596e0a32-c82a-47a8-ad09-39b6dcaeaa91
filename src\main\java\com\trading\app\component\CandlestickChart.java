package com.trading.app.component;

import com.trading.app.model.Candlestick;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.scene.canvas.Canvas;
import javafx.scene.canvas.GraphicsContext;
import javafx.scene.control.ScrollPane;
import javafx.scene.input.MouseEvent;
import javafx.scene.input.ScrollEvent;
import javafx.scene.layout.Pane;
import javafx.scene.paint.Color;
import javafx.scene.text.Font;

import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * Componente personalizado para mostrar gráficos de velas (candlestick)
 */
public class CandlestickChart extends Pane {
    private Canvas canvas;
    private GraphicsContext gc;
    private ObservableList<Candlestick> candlesticks;
    
    // Configuración del gráfico
    private double candleWidth = 8.0;
    private double candleSpacing = 2.0;
    private double marginTop = 30.0;
    private double marginBottom = 50.0;
    private double marginLeft = 60.0;
    private double marginRight = 20.0;
    
    // Colores
    private Color bullishColor = Color.GREEN;
    private Color bearishColor = Color.RED;
    private Color backgroundColor = Color.WHITE;
    private Color gridColor = Color.LIGHTGRAY;
    private Color textColor = Color.BLACK;
    
    // Zoom y pan
    private double zoomFactor = 1.0;
    private double panX = 0.0;
    private double panY = 0.0;
    private double lastMouseX = 0.0;
    private double lastMouseY = 0.0;
    
    // Rango de precios
    private double minPrice = Double.MAX_VALUE;
    private double maxPrice = Double.MIN_VALUE;
    
    public CandlestickChart() {
        this.candlesticks = FXCollections.observableArrayList();
        initializeCanvas();
        setupEventHandlers();
    }
    
    private void initializeCanvas() {
        canvas = new Canvas(800, 400);
        gc = canvas.getGraphicsContext2D();
        
        // Hacer que el canvas se redimensione con el contenedor
        canvas.widthProperty().bind(this.widthProperty());
        canvas.heightProperty().bind(this.heightProperty());
        
        // Redibujar cuando cambie el tamaño
        canvas.widthProperty().addListener(e -> redraw());
        canvas.heightProperty().addListener(e -> redraw());
        
        this.getChildren().add(canvas);
    }
    
    private void setupEventHandlers() {
        // Zoom con la rueda del mouse
        canvas.setOnScroll(this::handleScroll);
        
        // Pan con arrastrar el mouse
        canvas.setOnMousePressed(this::handleMousePressed);
        canvas.setOnMouseDragged(this::handleMouseDragged);
        
        // Hacer que el canvas pueda recibir focus para eventos
        canvas.setFocusTraversable(true);
    }
    
    private void handleScroll(ScrollEvent event) {
        double deltaY = event.getDeltaY();
        double scaleFactor = 1.1;
        
        if (deltaY > 0) {
            zoomFactor *= scaleFactor;
        } else {
            zoomFactor /= scaleFactor;
        }
        
        // Limitar el zoom
        zoomFactor = Math.max(0.1, Math.min(zoomFactor, 10.0));
        
        redraw();
        event.consume();
    }
    
    private void handleMousePressed(MouseEvent event) {
        lastMouseX = event.getX();
        lastMouseY = event.getY();
        canvas.requestFocus();
    }
    
    private void handleMouseDragged(MouseEvent event) {
        double deltaX = event.getX() - lastMouseX;
        double deltaY = event.getY() - lastMouseY;
        
        panX += deltaX;
        panY += deltaY;
        
        lastMouseX = event.getX();
        lastMouseY = event.getY();
        
        redraw();
    }
    
    /**
     * Establece los datos de las velas
     */
    public void setCandlesticks(List<Candlestick> candlesticks) {
        this.candlesticks.clear();
        this.candlesticks.addAll(candlesticks);
        calculatePriceRange();
        redraw();
    }
    
    /**
     * Agrega una nueva vela
     */
    public void addCandlestick(Candlestick candlestick) {
        this.candlesticks.add(candlestick);
        updatePriceRange(candlestick);
        redraw();
    }
    
    /**
     * Actualiza la última vela (para datos en tiempo real)
     */
    public void updateLastCandlestick(Candlestick candlestick) {
        if (!candlesticks.isEmpty()) {
            candlesticks.set(candlesticks.size() - 1, candlestick);
            updatePriceRange(candlestick);
            redraw();
        } else {
            addCandlestick(candlestick);
        }
    }
    
    private void calculatePriceRange() {
        minPrice = Double.MAX_VALUE;
        maxPrice = Double.MIN_VALUE;
        
        for (Candlestick candle : candlesticks) {
            minPrice = Math.min(minPrice, candle.getLow());
            maxPrice = Math.max(maxPrice, candle.getHigh());
        }
        
        // Agregar un pequeño margen
        double range = maxPrice - minPrice;
        double margin = range * 0.05;
        minPrice -= margin;
        maxPrice += margin;
    }
    
    private void updatePriceRange(Candlestick candlestick) {
        boolean needsRecalculation = false;
        
        if (candlestick.getLow() < minPrice) {
            minPrice = candlestick.getLow() - (maxPrice - minPrice) * 0.05;
            needsRecalculation = true;
        }
        
        if (candlestick.getHigh() > maxPrice) {
            maxPrice = candlestick.getHigh() + (maxPrice - minPrice) * 0.05;
            needsRecalculation = true;
        }
        
        if (needsRecalculation) {
            // Recalcular si es necesario
            calculatePriceRange();
        }
    }
    
    private void redraw() {
        if (candlesticks.isEmpty()) {
            clearCanvas();
            return;
        }
        
        clearCanvas();
        drawGrid();
        drawCandlesticks();
        drawPriceScale();
        drawTimeScale();
    }
    
    private void clearCanvas() {
        gc.setFill(backgroundColor);
        gc.fillRect(0, 0, canvas.getWidth(), canvas.getHeight());
    }
    
    private void drawGrid() {
        gc.setStroke(gridColor);
        gc.setLineWidth(0.5);
        
        double chartWidth = canvas.getWidth() - marginLeft - marginRight;
        double chartHeight = canvas.getHeight() - marginTop - marginBottom;
        
        // Líneas horizontales (precios)
        int horizontalLines = 10;
        for (int i = 0; i <= horizontalLines; i++) {
            double y = marginTop + (chartHeight * i / horizontalLines);
            gc.strokeLine(marginLeft, y, marginLeft + chartWidth, y);
        }
        
        // Líneas verticales (tiempo)
        int verticalLines = Math.min(candlesticks.size(), 20);
        if (verticalLines > 0) {
            for (int i = 0; i <= verticalLines; i++) {
                double x = marginLeft + (chartWidth * i / verticalLines);
                gc.strokeLine(x, marginTop, x, marginTop + chartHeight);
            }
        }
    }
    
    private void drawCandlesticks() {
        if (candlesticks.isEmpty() || maxPrice <= minPrice) return;
        
        double chartWidth = canvas.getWidth() - marginLeft - marginRight;
        double chartHeight = canvas.getHeight() - marginTop - marginBottom;
        double priceRange = maxPrice - minPrice;
        
        double totalCandleWidth = (candleWidth + candleSpacing) * zoomFactor;
        double startX = marginLeft + panX;
        
        for (int i = 0; i < candlesticks.size(); i++) {
            Candlestick candle = candlesticks.get(i);
            double x = startX + (i * totalCandleWidth);
            
            // Solo dibujar velas visibles
            if (x + candleWidth < 0 || x > canvas.getWidth()) continue;
            
            drawCandlestick(candle, x, chartHeight, priceRange);
        }
    }
    
    private void drawCandlestick(Candlestick candle, double x, double chartHeight, double priceRange) {
        double scaledCandleWidth = candleWidth * zoomFactor;
        
        // Calcular posiciones Y
        double highY = marginTop + ((maxPrice - candle.getHigh()) / priceRange) * chartHeight;
        double lowY = marginTop + ((maxPrice - candle.getLow()) / priceRange) * chartHeight;
        double openY = marginTop + ((maxPrice - candle.getOpen()) / priceRange) * chartHeight;
        double closeY = marginTop + ((maxPrice - candle.getClose()) / priceRange) * chartHeight;
        
        // Color de la vela
        Color candleColor = candle.isBullish() ? bullishColor : bearishColor;
        
        // Dibujar la mecha (línea vertical)
        gc.setStroke(candleColor);
        gc.setLineWidth(1.0);
        double centerX = x + scaledCandleWidth / 2;
        gc.strokeLine(centerX, highY, centerX, lowY);
        
        // Dibujar el cuerpo
        gc.setFill(candleColor);
        double bodyTop = Math.min(openY, closeY);
        double bodyHeight = Math.abs(closeY - openY);
        
        if (bodyHeight < 1.0) bodyHeight = 1.0; // Mínimo visible
        
        if (candle.isBullish()) {
            // Vela alcista - solo contorno
            gc.setStroke(candleColor);
            gc.setLineWidth(1.0);
            gc.strokeRect(x, bodyTop, scaledCandleWidth, bodyHeight);
        } else {
            // Vela bajista - rellena
            gc.fillRect(x, bodyTop, scaledCandleWidth, bodyHeight);
        }
    }
    
    private void drawPriceScale() {
        if (maxPrice <= minPrice) return;
        
        gc.setFill(textColor);
        gc.setFont(Font.font(10));
        
        double chartHeight = canvas.getHeight() - marginTop - marginBottom;
        int priceLines = 10;
        
        for (int i = 0; i <= priceLines; i++) {
            double price = minPrice + ((maxPrice - minPrice) * i / priceLines);
            double y = marginTop + (chartHeight * (priceLines - i) / priceLines);
            
            String priceText = String.format("%.5f", price);
            gc.fillText(priceText, 5, y + 3);
        }
    }
    
    private void drawTimeScale() {
        if (candlesticks.isEmpty()) return;
        
        gc.setFill(textColor);
        gc.setFont(Font.font(10));
        
        double chartWidth = canvas.getWidth() - marginLeft - marginRight;
        double totalCandleWidth = (candleWidth + candleSpacing) * zoomFactor;
        
        // Mostrar cada N velas dependiendo del zoom
        int step = Math.max(1, (int)(50 / totalCandleWidth));
        
        for (int i = 0; i < candlesticks.size(); i += step) {
            if (i >= candlesticks.size()) break;
            
            Candlestick candle = candlesticks.get(i);
            double x = marginLeft + panX + (i * totalCandleWidth);
            
            if (x < marginLeft || x > marginLeft + chartWidth) continue;
            
            String timeText = candle.getTimestamp().format(DateTimeFormatter.ofPattern("HH:mm"));
            gc.fillText(timeText, x, canvas.getHeight() - 10);
        }
    }
    
    // Getters y setters para personalización
    public void setBullishColor(Color color) {
        this.bullishColor = color;
        redraw();
    }
    
    public void setBearishColor(Color color) {
        this.bearishColor = color;
        redraw();
    }
    
    public void setCandleWidth(double width) {
        this.candleWidth = width;
        redraw();
    }
    
    public ObservableList<Candlestick> getCandlesticks() {
        return candlesticks;
    }
}
