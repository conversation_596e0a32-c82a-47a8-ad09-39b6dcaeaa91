package com.trading.app.indicator;

import com.trading.app.model.Candlestick;
import javafx.scene.canvas.GraphicsContext;
import javafx.scene.paint.Color;

import java.util.List;

/**
 * Indicador de Media Móvil Simple (SMA)
 */
public class MovingAverageIndicator extends TechnicalIndicator {

    public MovingAverageIndicator(int period) {
        super("SMA", Color.BLUE, period);
    }

    public MovingAverageIndicator(int period, Color color) {
        super("SMA", color, period);
    }

    @Override
    public double[] calculate(List<Candlestick> candlesticks) {
        if (candlesticks == null || candlesticks.size() < period) {
            return new double[0];
        }

        double[] smaValues = new double[candlesticks.size()];
        
        for (int i = 0; i < candlesticks.size(); i++) {
            if (i < period - 1) {
                smaValues[i] = Double.NaN;
            } else {
                double sum = 0;
                for (int j = i - period + 1; j <= i; j++) {
                    sum += candlesticks.get(j).getClose();
                }
                smaValues[i] = sum / period;
            }
        }
        
        return smaValues;
    }

    @Override
    public void draw(GraphicsContext gc, List<Candlestick> candlesticks, double[] values,
                     double chartWidth, double chartHeight, double marginLeft, double marginTop,
                     double minPrice, double maxPrice, double candleWidth, double zoomFactor, double panX) {
        
        if (!visible || values == null || values.length == 0) return;
        
        drawLine(gc, candlesticks, values, chartWidth, chartHeight, marginLeft, marginTop,
                minPrice, maxPrice, candleWidth, zoomFactor, panX);
    }

    @Override
    public boolean isOverlay() {
        return true; // Se dibuja sobre el gráfico principal
    }
}
