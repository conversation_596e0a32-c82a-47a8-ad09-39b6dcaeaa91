package com.trading.app.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.trading.app.model.HistoricalData;
import com.trading.app.model.Price;
import javafx.concurrent.Task;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;

/**
 * Servicio para comunicarse con MetaTrader 5 a través del script Python
 */
public class MT5Service {
    private static final Logger logger = LoggerFactory.getLogger(MT5Service.class);
    private final ObjectMapper objectMapper;
    private Process pythonProcess;
    private boolean isConnected = false;

    public MT5Service() {
        this.objectMapper = new ObjectMapper();
    }

    /**
     * Inicializa la conexión con MT5
     */
    public CompletableFuture<Boolean> initialize() {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // Ejecutar el script Python que se conecta a MT5
                ProcessBuilder pb = new ProcessBuilder("python", "python/mt5_bridge.py", "init");
                pb.redirectErrorStream(true);
                pythonProcess = pb.start();
                
                BufferedReader reader = new BufferedReader(new InputStreamReader(pythonProcess.getInputStream()));
                String line = reader.readLine();
                
                if (line != null && line.contains("\"status\": \"connected\"")) {
                    isConnected = true;
                    logger.info("Conexión con MT5 establecida exitosamente: " + line);
                    return true;
                } else {
                    logger.error("Error al conectar con MT5: " + line);
                    return false;
                }
            } catch (IOException e) {
                logger.error("Error al inicializar MT5Service", e);
                return false;
            }
        });
    }

    /**
     * Obtiene el precio actual de un símbolo
     */
    public CompletableFuture<Price> getCurrentPrice(String symbol) {
        return CompletableFuture.supplyAsync(() -> {
            if (!isConnected) {
                throw new RuntimeException("No hay conexión con MT5");
            }

            try {
                ProcessBuilder pb = new ProcessBuilder("python", "python/mt5_bridge.py", "price", symbol);
                pb.redirectErrorStream(true);
                Process process = pb.start();
                
                BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                String jsonResponse = reader.readLine();
                
                if (jsonResponse != null) {
                    JsonNode node = objectMapper.readTree(jsonResponse);
                    if (node.has("error")) {
                        throw new RuntimeException("Error desde MT5: " + node.get("error").asText());
                    }
                    
                    return new Price(
                        symbol,
                        node.get("bid").asDouble(),
                        node.get("ask").asDouble(),
                        node.get("last").asDouble(),
                        node.get("volume").asLong()
                    );
                }
                
                throw new RuntimeException("No se recibió respuesta válida de MT5");
                
            } catch (Exception e) {
                logger.error("Error al obtener precio para " + symbol, e);
                throw new RuntimeException(e);
            }
        });
    }

    /**
     * Obtiene datos históricos para un símbolo
     */
    public CompletableFuture<List<HistoricalData>> getHistoricalData(String symbol, String timeframe, int count) {
        return CompletableFuture.supplyAsync(() -> {
            if (!isConnected) {
                throw new RuntimeException("No hay conexión con MT5");
            }

            try {
                ProcessBuilder pb = new ProcessBuilder("python", "python/mt5_bridge.py", "history", symbol, timeframe, String.valueOf(count));
                pb.redirectErrorStream(true);
                Process process = pb.start();
                
                BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                String jsonResponse = reader.readLine();
                
                if (jsonResponse != null) {
                    JsonNode node = objectMapper.readTree(jsonResponse);
                    if (node.has("error")) {
                        throw new RuntimeException("Error desde MT5: " + node.get("error").asText());
                    }
                    
                    List<HistoricalData> historicalDataList = new ArrayList<>();
                    JsonNode dataArray = node.get("data");
                    
                    for (JsonNode dataNode : dataArray) {
                        HistoricalData data = new HistoricalData(
                            symbol,
                            LocalDateTime.parse(dataNode.get("time").asText(), DateTimeFormatter.ISO_LOCAL_DATE_TIME),
                            dataNode.get("open").asDouble(),
                            dataNode.get("high").asDouble(),
                            dataNode.get("low").asDouble(),
                            dataNode.get("close").asDouble(),
                            dataNode.get("volume").asLong(),
                            timeframe
                        );
                        historicalDataList.add(data);
                    }
                    
                    return historicalDataList;
                }
                
                throw new RuntimeException("No se recibió respuesta válida de MT5");
                
            } catch (Exception e) {
                logger.error("Error al obtener datos históricos para " + symbol, e);
                throw new RuntimeException(e);
            }
        });
    }

    /**
     * Inicia el streaming de precios en tiempo real
     */
    public Task<Void> startPriceStream(String symbol, Consumer<Price> priceConsumer) {
        return new Task<Void>() {
            @Override
            protected Void call() throws Exception {
                if (!isConnected) {
                    throw new RuntimeException("No hay conexión con MT5");
                }

                try {
                    ProcessBuilder pb = new ProcessBuilder("python", "python/mt5_bridge.py", "stream", symbol);
                    pb.redirectErrorStream(true);
                    Process streamProcess = pb.start();
                    
                    BufferedReader reader = new BufferedReader(new InputStreamReader(streamProcess.getInputStream()));
                    String line;
                    
                    while ((line = reader.readLine()) != null && !isCancelled()) {
                        try {
                            JsonNode node = objectMapper.readTree(line);
                            if (!node.has("error")) {
                                Price price = new Price(
                                    symbol,
                                    node.get("bid").asDouble(),
                                    node.get("ask").asDouble(),
                                    node.get("last").asDouble(),
                                    node.get("volume").asLong()
                                );
                                priceConsumer.accept(price);
                            }
                        } catch (Exception e) {
                            logger.warn("Error al procesar precio en streaming", e);
                        }
                    }
                    
                } catch (Exception e) {
                    logger.error("Error en streaming de precios", e);
                    throw e;
                }
                
                return null;
            }
        };
    }

    /**
     * Cierra la conexión con MT5
     */
    public void shutdown() {
        if (pythonProcess != null && pythonProcess.isAlive()) {
            pythonProcess.destroy();
        }
        isConnected = false;
        logger.info("Conexión con MT5 cerrada");
    }

    public boolean isConnected() {
        return isConnected;
    }
}
