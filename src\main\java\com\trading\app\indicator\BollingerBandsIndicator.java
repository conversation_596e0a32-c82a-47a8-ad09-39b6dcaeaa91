package com.trading.app.indicator;

import com.trading.app.model.Candlestick;
import javafx.scene.canvas.GraphicsContext;
import javafx.scene.paint.Color;

import java.util.List;

/**
 * Indicador de Bandas de Bollinger
 */
public class BollingerBandsIndicator extends TechnicalIndicator {
    private double standardDeviations;
    private double[] upperBand;
    private double[] middleBand;
    private double[] lowerBand;

    public BollingerBandsIndicator(int period) {
        this(period, 2.0);
    }

    public BollingerBandsIndicator(int period, double standardDeviations) {
        super("Bollinger Bands", Color.GRAY, period);
        this.standardDeviations = standardDeviations;
    }

    @Override
    public double[] calculate(List<Candlestick> candlesticks) {
        if (candlesticks == null || candlesticks.size() < period) {
            return new double[0];
        }

        int size = candlesticks.size();
        middleBand = new double[size]; // SMA
        upperBand = new double[size];
        lowerBand = new double[size];
        
        // Calcular SMA (banda media)
        for (int i = 0; i < size; i++) {
            if (i < period - 1) {
                middleBand[i] = Double.NaN;
                upperBand[i] = Double.NaN;
                lowerBand[i] = Double.NaN;
            } else {
                // Calcular SMA
                double sum = 0;
                for (int j = i - period + 1; j <= i; j++) {
                    sum += candlesticks.get(j).getClose();
                }
                middleBand[i] = sum / period;
                
                // Calcular desviación estándar
                double variance = 0;
                for (int j = i - period + 1; j <= i; j++) {
                    double diff = candlesticks.get(j).getClose() - middleBand[i];
                    variance += diff * diff;
                }
                double stdDev = Math.sqrt(variance / period);
                
                // Calcular bandas superior e inferior
                upperBand[i] = middleBand[i] + (standardDeviations * stdDev);
                lowerBand[i] = middleBand[i] - (standardDeviations * stdDev);
            }
        }
        
        return middleBand; // Retorna la banda media
    }

    @Override
    public void draw(GraphicsContext gc, List<Candlestick> candlesticks, double[] values,
                     double chartWidth, double chartHeight, double marginLeft, double marginTop,
                     double minPrice, double maxPrice, double candleWidth, double zoomFactor, double panX) {
        
        if (!visible || values == null || values.length == 0) return;
        
        // Dibujar banda superior
        if (upperBand != null) {
            gc.setStroke(color);
            gc.setLineWidth(1.0);
            drawLine(gc, candlesticks, upperBand, chartWidth, chartHeight, marginLeft, marginTop,
                    minPrice, maxPrice, candleWidth, zoomFactor, panX);
        }
        
        // Dibujar banda media (SMA)
        if (middleBand != null) {
            gc.setStroke(color.deriveColor(0, 1, 1, 0.8));
            gc.setLineWidth(1.5);
            drawLine(gc, candlesticks, middleBand, chartWidth, chartHeight, marginLeft, marginTop,
                    minPrice, maxPrice, candleWidth, zoomFactor, panX);
        }
        
        // Dibujar banda inferior
        if (lowerBand != null) {
            gc.setStroke(color);
            gc.setLineWidth(1.0);
            drawLine(gc, candlesticks, lowerBand, chartWidth, chartHeight, marginLeft, marginTop,
                    minPrice, maxPrice, candleWidth, zoomFactor, panX);
        }
        
        // Rellenar área entre bandas
        drawBandFill(gc, candlesticks, chartWidth, chartHeight, marginLeft, marginTop,
                    minPrice, maxPrice, candleWidth, zoomFactor, panX);
    }

    private void drawBandFill(GraphicsContext gc, List<Candlestick> candlesticks,
                             double chartWidth, double chartHeight, double marginLeft, double marginTop,
                             double minPrice, double maxPrice, double candleWidth, double zoomFactor, double panX) {
        
        if (upperBand == null || lowerBand == null) return;
        
        gc.setFill(color.deriveColor(0, 1, 1, 0.1));
        
        double totalCandleWidth = (candleWidth + 2) * zoomFactor;
        double startX = marginLeft + panX;
        double priceRange = maxPrice - minPrice;
        
        for (int i = 0; i < Math.min(upperBand.length - 1, candlesticks.size() - 1); i++) {
            if (Double.isNaN(upperBand[i]) || Double.isNaN(lowerBand[i]) ||
                Double.isNaN(upperBand[i + 1]) || Double.isNaN(lowerBand[i + 1])) continue;
            
            double x1 = startX + (i * totalCandleWidth);
            double x2 = startX + ((i + 1) * totalCandleWidth);
            
            // Solo dibujar segmentos visibles
            if (x2 < 0 || x1 > chartWidth + marginLeft) continue;
            
            double y1Upper = marginTop + ((maxPrice - upperBand[i]) / priceRange) * chartHeight;
            double y1Lower = marginTop + ((maxPrice - lowerBand[i]) / priceRange) * chartHeight;
            double y2Upper = marginTop + ((maxPrice - upperBand[i + 1]) / priceRange) * chartHeight;
            double y2Lower = marginTop + ((maxPrice - lowerBand[i + 1]) / priceRange) * chartHeight;
            
            // Dibujar polígono para rellenar el área
            double[] xPoints = {x1, x2, x2, x1};
            double[] yPoints = {y1Upper, y2Upper, y2Lower, y1Lower};
            
            gc.fillPolygon(xPoints, yPoints, 4);
        }
    }

    @Override
    public boolean isOverlay() {
        return true; // Se dibuja sobre el gráfico principal
    }

    // Getters para acceder a las bandas individuales
    public double[] getUpperBand() {
        return upperBand;
    }

    public double[] getMiddleBand() {
        return middleBand;
    }

    public double[] getLowerBand() {
        return lowerBand;
    }

    public double getStandardDeviations() {
        return standardDeviations;
    }

    public void setStandardDeviations(double standardDeviations) {
        this.standardDeviations = standardDeviations;
    }

    @Override
    public String toString() {
        return name + " (" + period + ", " + standardDeviations + ")";
    }
}
