package com.trading.app.indicator;

import com.trading.app.model.Candlestick;
import javafx.scene.canvas.GraphicsContext;
import javafx.scene.paint.Color;

import java.util.List;

/**
 * Clase base abstracta para todos los indicadores técnicos
 */
public abstract class TechnicalIndicator {
    protected String name;
    protected Color color;
    protected boolean visible;
    protected double lineWidth;
    protected int period;

    public TechnicalIndicator(String name, Color color, int period) {
        this.name = name;
        this.color = color;
        this.period = period;
        this.visible = true;
        this.lineWidth = 1.5;
    }

    /**
     * Calcula los valores del indicador basado en las velas
     */
    public abstract double[] calculate(List<Candlestick> candlesticks);

    /**
     * Dibuja el indicador en el gráfico
     */
    public abstract void draw(GraphicsContext gc, List<Candlestick> candlesticks, 
                             double[] values, double chartWidth, double chartHeight, 
                             double marginLeft, double marginTop, double minPrice, 
                             double maxPrice, double candleWidth, double zoomFactor, double panX);

    /**
     * Determina si el indicador se dibuja en el panel principal o en un subpanel
     */
    public abstract boolean isOverlay();

    /**
     * Obtiene el rango de valores para el indicador (para subpaneles)
     */
    public double[] getValueRange(double[] values) {
        if (values == null || values.length == 0) {
            return new double[]{0, 100};
        }
        
        double min = Double.MAX_VALUE;
        double max = Double.MIN_VALUE;
        
        for (double value : values) {
            if (!Double.isNaN(value)) {
                min = Math.min(min, value);
                max = Math.max(max, value);
            }
        }
        
        // Agregar un pequeño margen
        double range = max - min;
        double margin = range * 0.1;
        
        return new double[]{min - margin, max + margin};
    }

    /**
     * Método helper para dibujar líneas
     */
    protected void drawLine(GraphicsContext gc, List<Candlestick> candlesticks, double[] values,
                           double chartWidth, double chartHeight, double marginLeft, double marginTop,
                           double minValue, double maxValue, double candleWidth, double zoomFactor, double panX) {
        
        if (values == null || values.length == 0) return;
        
        gc.setStroke(color);
        gc.setLineWidth(lineWidth);
        
        double totalCandleWidth = (candleWidth + 2) * zoomFactor;
        double startX = marginLeft + panX;
        double valueRange = maxValue - minValue;
        
        Double lastX = null;
        Double lastY = null;
        
        for (int i = 0; i < Math.min(values.length, candlesticks.size()); i++) {
            if (Double.isNaN(values[i])) continue;
            
            double x = startX + (i * totalCandleWidth) + (candleWidth * zoomFactor / 2);
            
            // Solo dibujar puntos visibles
            if (x < 0 || x > chartWidth + marginLeft) continue;
            
            double y = marginTop + ((maxValue - values[i]) / valueRange) * chartHeight;
            
            if (lastX != null && lastY != null) {
                gc.strokeLine(lastX, lastY, x, y);
            }
            
            lastX = x;
            lastY = y;
        }
    }

    // Getters y Setters
    public String getName() {
        return name;
    }

    public Color getColor() {
        return color;
    }

    public void setColor(Color color) {
        this.color = color;
    }

    public boolean isVisible() {
        return visible;
    }

    public void setVisible(boolean visible) {
        this.visible = visible;
    }

    public double getLineWidth() {
        return lineWidth;
    }

    public void setLineWidth(double lineWidth) {
        this.lineWidth = lineWidth;
    }

    public int getPeriod() {
        return period;
    }

    public void setPeriod(int period) {
        this.period = period;
    }

    @Override
    public String toString() {
        return name + " (" + period + ")";
    }
}
