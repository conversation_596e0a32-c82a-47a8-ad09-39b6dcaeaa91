package com.trading.app.model;

import java.time.LocalDateTime;

/**
 * Modelo para representar una vela (candlestick) en el gráfico
 */
public class Candlestick {
    private String symbol;
    private LocalDateTime timestamp;
    private double open;
    private double high;
    private double low;
    private double close;
    private long volume;
    private String timeframe;
    private boolean isComplete; // Indica si la vela está completa o aún se está formando

    public Candlestick() {
        this.timestamp = LocalDateTime.now();
        this.isComplete = false;
    }

    public Candlestick(String symbol, LocalDateTime timestamp, double open, double high, 
                      double low, double close, long volume, String timeframe) {
        this.symbol = symbol;
        this.timestamp = timestamp;
        this.open = open;
        this.high = high;
        this.low = low;
        this.close = close;
        this.volume = volume;
        this.timeframe = timeframe;
        this.isComplete = true;
    }

    /**
     * Crea una vela desde datos históricos
     */
    public static Candlestick fromHistoricalData(HistoricalData data) {
        return new Candlestick(
            data.getSymbol(),
            data.getTimestamp(),
            data.getOpen(),
            data.getHigh(),
            data.getLow(),
            data.getClose(),
            data.getVolume(),
            data.getTimeframe()
        );
    }

    /**
     * Actualiza la vela con un nuevo precio (para velas en tiempo real)
     */
    public void updateWithPrice(double price) {
        if (this.open == 0) {
            this.open = price;
            this.high = price;
            this.low = price;
        }
        
        this.close = price;
        this.high = Math.max(this.high, price);
        this.low = Math.min(this.low, price);
        this.timestamp = LocalDateTime.now();
    }

    /**
     * Determina si la vela es alcista (verde) o bajista (roja)
     */
    public boolean isBullish() {
        return close >= open;
    }

    /**
     * Calcula el cuerpo de la vela
     */
    public double getBodySize() {
        return Math.abs(close - open);
    }

    /**
     * Calcula la sombra superior
     */
    public double getUpperShadow() {
        return high - Math.max(open, close);
    }

    /**
     * Calcula la sombra inferior
     */
    public double getLowerShadow() {
        return Math.min(open, close) - low;
    }

    /**
     * Calcula el rango total de la vela
     */
    public double getRange() {
        return high - low;
    }

    // Getters and Setters
    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public double getOpen() {
        return open;
    }

    public void setOpen(double open) {
        this.open = open;
    }

    public double getHigh() {
        return high;
    }

    public void setHigh(double high) {
        this.high = high;
    }

    public double getLow() {
        return low;
    }

    public void setLow(double low) {
        this.low = low;
    }

    public double getClose() {
        return close;
    }

    public void setClose(double close) {
        this.close = close;
    }

    public long getVolume() {
        return volume;
    }

    public void setVolume(long volume) {
        this.volume = volume;
    }

    public String getTimeframe() {
        return timeframe;
    }

    public void setTimeframe(String timeframe) {
        this.timeframe = timeframe;
    }

    public boolean isComplete() {
        return isComplete;
    }

    public void setComplete(boolean complete) {
        isComplete = complete;
    }

    @Override
    public String toString() {
        return String.format("Candlestick{symbol='%s', timestamp=%s, OHLC=(%.5f,%.5f,%.5f,%.5f), volume=%d, timeframe='%s', complete=%s}",
                symbol, timestamp, open, high, low, close, volume, timeframe, isComplete);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        Candlestick that = (Candlestick) obj;
        return symbol.equals(that.symbol) && 
               timestamp.equals(that.timestamp) && 
               timeframe.equals(that.timeframe);
    }

    @Override
    public int hashCode() {
        return symbol.hashCode() + timestamp.hashCode() + timeframe.hashCode();
    }
}
